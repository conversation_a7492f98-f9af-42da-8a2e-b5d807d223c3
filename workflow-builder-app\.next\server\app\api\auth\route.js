const CHUNK_PUBLIC_PATH = "server/app/api/auth/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_authApi_ts_3a74264a._.js");
runtime.loadChunk("server/chunks/node_modules_next_a8b97aa3._.js");
runtime.loadChunk("server/chunks/node_modules_axios_lib_c4a55de8._.js");
runtime.loadChunk("server/chunks/node_modules_mime-db_9ebaabbe._.js");
runtime.loadChunk("server/chunks/node_modules_0f1ec155._.js");
runtime.loadChunk("server/chunks/[root of the server]__b4965dc2._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/auth/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
