# AgenticAI Component - Updated Schema Documentation

## Summary

Successfully updated the AgenticAI component to support the orchestration engine's specific JSON format requirements. The component now includes all required fields while maintaining backward compatibility and following established workflow platform patterns.

## ✅ Implementation Status

**COMPLETED:**
- ✅ Added all required fields (`execution_type`, `query`, `name`, `termination_condition`, `description`, `system_message`, `max_tokens`, `tools`, `id`)
- ✅ Modified `agent_type` to be hardcoded as "component" (removed from user inputs)
- ✅ Implemented conditional field visibility (`termination_condition` only shows for interactive agents)
- ✅ Added proper validation rules and default values
- ✅ Created `get_agent_config()` method for orchestration engine integration
- ✅ Maintained backward compatibility with existing patterns
- ✅ All tests passing

## User-Configurable Fields (Exposed in Workflow Builder UI)

### 🔧 Core Agent Configuration
| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| `id` | string | No | auto-generated | Unique agent identifier |
| `name` | string | No | "AI Agent" | Display name for the agent |
| `description` | string | No | "" | Agent description for UI display |
| `execution_type` | enum | No | "response" | "response" or "interactive" |
| `query` | string | **Yes** | - | Task/objective for the agent |
| `system_message` | string | No | "" | System prompt/instructions |
| `termination_condition` | string | Conditional* | "" | When to end conversations |
| `max_tokens` | number | No | 1000 | Maximum response length |

*Required only when `execution_type` is "interactive"

### 🤖 Model Configuration (Inherited from BaseAgentComponent)
| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| `model_provider` | dropdown | No | "OpenAI" | AI model provider |
| `base_url` | string | No | "" | Custom API endpoint |
| `api_key` | credential | No | "" | Provider API key |
| `model_name` | dropdown | No | "" | Specific model to use |
| `temperature` | float | No | 0.7 | Response randomness |

### 📊 Data & Tools
| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| `input_variables` | dict | No | {} | Variables for the agent |
| `tools` | list | No | [] | Available tools/functions |
| `memory` | handle | No | null | Memory object connection |

### ⚙️ Advanced Options
| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| `autogen_agent_type` | dropdown | No | "Assistant" | Internal AutoGen agent type |
| `stream` | boolean | No | false | Enable response streaming |

## Hardcoded Values (Not User-Configurable)

### 🔒 Orchestration Engine Compatibility
- **`agent_type`**: Always returns `"component"` (implemented as property method)

## Orchestration Engine Output Format

The `get_agent_config()` method generates this JSON structure:

```json
{
  "id": "agent_<node_id>",
  "name": "AI Agent",
  "description": "",
  "agent_type": "component",
  "execution_type": "response",
  "query": "User's query/objective",
  "system_message": "System prompt",
  "tools": [],
  "agent_config": {
    "model_provider": "OpenAI",
    "model_name": "gpt-4-turbo",
    "temperature": 0.7,
    "max_tokens": 1000
  }
}
```

**For Interactive Agents Only:**
```json
{
  "termination_condition": "User says 'goodbye' or exceeds 10 turns"
}
```

## Validation Rules

1. **Required Fields**: `query` is always required
2. **Conditional Requirements**: `termination_condition` required when `execution_type` = "interactive"
3. **Visibility Rules**: `termination_condition` only visible for interactive agents
4. **Auto-generation**: `id` auto-generated if empty using pattern `agent_{node_id}`

## Key Architectural Decisions

### ✅ User-Configurable (Workflow-Specific)
Fields that vary per workflow use case:
- Agent identity (`id`, `name`, `description`)
- Execution behavior (`execution_type`, `query`, `system_message`, `termination_condition`)
- Model settings (`max_tokens`, `temperature`, `model_name`)
- Data inputs (`input_variables`, `tools`)

### 🔒 Hardcoded (Platform-Standard)
Fields that are standard across the platform:
- `agent_type`: Always "component" for orchestration engine compatibility

### 🔄 Dual-Purpose Inputs
These fields support both direct input and node connections:
- `query` (main task/objective)
- `input_variables` (context data)
- `tools` (available functions)

## Migration Notes

### Breaking Changes
- **`objective` → `query`**: Existing workflows using `objective` need to be updated to use `query`

### Backward Compatibility
- All other existing functionality preserved
- Legacy `build` method maintained with deprecation warning
- Existing input patterns and validation maintained

## Testing Verification

All tests pass successfully:
- ✅ Component properties and metadata
- ✅ Agent configuration generation
- ✅ Interactive agent with termination conditions
- ✅ Input validation and default values
- ✅ Orchestration engine format compliance

## Usage Example

```python
# Get agent configuration for orchestration engine
agent_config = component.get_agent_config(context)

# Example output for response-type agent
{
    "id": "agent_node_123",
    "name": "Data Analyzer",
    "agent_type": "component",
    "execution_type": "response",
    "query": "Analyze sales data and provide insights",
    "tools": ["calculator", "chart_generator"],
    "agent_config": {
        "model_provider": "OpenAI",
        "model_name": "gpt-4-turbo",
        "temperature": 0.7,
        "max_tokens": 1000
    }
}
```

The updated AgenticAI component now fully supports the orchestration engine's requirements while maintaining the workflow platform's established patterns and user experience.
