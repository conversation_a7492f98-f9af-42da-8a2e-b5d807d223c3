#!/usr/bin/env python3
"""
Test script to verify the updated AgenticAI component configuration.
"""

import sys
import os
import asyncio
from typing import Dict, Any

# Add the workflow-service to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'workflow-service'))

from app.components.ai.agentic_ai import AgenticAI
from app.models.workflow_builder.context import WorkflowContext


class MockWorkflowContext:
    """Mock WorkflowContext for testing."""
    
    def __init__(self, node_outputs: Dict[str, Any] = None):
        self.current_node_id = "test-agent-node"
        self.node_outputs = node_outputs or {}
        self.logs = []
    
    def log(self, message: str):
        """Mock log method."""
        self.logs.append(message)
        print(f"[LOG] {message}")


def test_agent_config_generation():
    """Test agent configuration generation for orchestration engine."""
    print("Testing Agent Configuration Generation...")
    
    # Create component instance
    component = AgenticAI()
    
    # Test data
    test_inputs = {
        "test-agent-node": {
            "id": "custom-agent-123",
            "name": "Test AI Agent",
            "description": "A test agent for validation",
            "execution_type": "response",
            "query": "Analyze the provided data and generate insights",
            "system_message": "You are a data analysis expert",
            "max_tokens": 2000,
            "tools": ["calculator", "web_search"],
            "model_provider": "OpenAI",
            "model_name": "gpt-4-turbo",
            "temperature": 0.8,
        }
    }
    
    # Create mock context
    context = MockWorkflowContext(test_inputs)
    
    # Test agent configuration generation
    agent_config = component.get_agent_config(context)
    
    print("\nGenerated Agent Configuration:")
    print("=" * 50)
    for key, value in agent_config.items():
        print(f"{key}: {value}")
    
    # Validate required fields
    required_fields = ["id", "name", "agent_type", "execution_type", "query", "agent_config"]
    missing_fields = [field for field in required_fields if field not in agent_config]
    
    if missing_fields:
        print(f"\n❌ FAILED: Missing required fields: {missing_fields}")
        return False
    
    # Validate agent_type is hardcoded to "component"
    if agent_config["agent_type"] != "component":
        print(f"❌ FAILED: agent_type should be 'component', got '{agent_config['agent_type']}'")
        return False
    
    print("\n✅ PASSED: Agent configuration generation test")
    return True


def test_interactive_agent_config():
    """Test interactive agent configuration with termination condition."""
    print("\nTesting Interactive Agent Configuration...")
    
    component = AgenticAI()
    
    # Test data for interactive agent
    test_inputs = {
        "test-agent-node": {
            "execution_type": "interactive",
            "query": "Help the user with their questions",
            "termination_condition": "User says 'goodbye' or conversation exceeds 10 turns",
            "name": "Interactive Assistant",
        }
    }
    
    context = MockWorkflowContext(test_inputs)
    agent_config = component.get_agent_config(context)
    
    # Validate termination condition is included for interactive agents
    if "termination_condition" not in agent_config:
        print("❌ FAILED: termination_condition missing for interactive agent")
        return False
    
    if agent_config["execution_type"] != "interactive":
        print("❌ FAILED: execution_type should be 'interactive'")
        return False
    
    print("✅ PASSED: Interactive agent configuration test")
    return True


def test_input_validation():
    """Test input validation and default values."""
    print("\nTesting Input Validation and Defaults...")
    
    component = AgenticAI()
    
    # Test with minimal inputs
    test_inputs = {
        "test-agent-node": {
            "query": "Simple test query",
        }
    }
    
    context = MockWorkflowContext(test_inputs)
    agent_config = component.get_agent_config(context)
    
    # Check default values
    expected_defaults = {
        "name": "AI Agent",
        "description": "",
        "execution_type": "response",
        "agent_type": "component",
        "tools": [],
    }
    
    for key, expected_value in expected_defaults.items():
        if agent_config.get(key) != expected_value:
            print(f"❌ FAILED: Default value for {key} should be {expected_value}, got {agent_config.get(key)}")
            return False
    
    # Check agent_config nested object
    if "agent_config" not in agent_config:
        print("❌ FAILED: agent_config nested object missing")
        return False
    
    nested_config = agent_config["agent_config"]
    if nested_config.get("max_tokens") != 1000:  # Default value
        print(f"❌ FAILED: Default max_tokens should be 1000, got {nested_config.get('max_tokens')}")
        return False
    
    print("✅ PASSED: Input validation and defaults test")
    return True


def test_component_properties():
    """Test component properties and metadata."""
    print("\nTesting Component Properties...")
    
    component = AgenticAI()
    
    # Test hardcoded agent_type property
    if component.agent_type != "component":
        print(f"❌ FAILED: agent_type property should return 'component', got '{component.agent_type}'")
        return False
    
    # Test component metadata
    if component.name != "AgenticAI":
        print(f"❌ FAILED: Component name should be 'AgenticAI', got '{component.name}'")
        return False
    
    if component.display_name != "AI Agent Executor":
        print(f"❌ FAILED: Display name should be 'AI Agent Executor', got '{component.display_name}'")
        return False
    
    # Test inputs are properly defined
    input_names = [inp.name for inp in component.inputs]
    required_inputs = ["id", "name", "description", "execution_type", "query", "max_tokens"]
    
    missing_inputs = [inp for inp in required_inputs if inp not in input_names]
    if missing_inputs:
        print(f"❌ FAILED: Missing input definitions: {missing_inputs}")
        return False
    
    print("✅ PASSED: Component properties test")
    return True


def main():
    """Run all tests."""
    print("AgenticAI Component Configuration Tests")
    print("=" * 60)
    
    tests = [
        test_component_properties,
        test_agent_config_generation,
        test_interactive_agent_config,
        test_input_validation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ FAILED: {test.__name__} - Exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The agent configuration schema is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
