{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/utils/authCookies.ts"], "sourcesContent": ["// src/utils/authCookies.ts\r\nimport { getC<PERSON>ie, setC<PERSON>ie, deleteCookie } from \"cookies-next\";\r\n\r\n// <PERSON><PERSON> names\r\nexport const ACCESS_TOKEN_NAME = \"access_token\";\r\nexport const REFRESH_TOKEN_NAME = \"refresh_token\";\r\n\r\n/**\r\n * Get the access token from cookies\r\n * @returns The access token or null if not found\r\n */\r\nexport const getAccessToken = async (): Promise<string | null> => {\r\n  const token = getCookie(ACCESS_TOKEN_NAME);\r\n  return token ? String(token) : null;\r\n};\r\n\r\n/**\r\n * Get the refresh token from cookies\r\n * @returns The refresh token or null if not found\r\n */\r\nexport const getRefreshToken = async (): Promise<string | null> => {\r\n  const token = getCookie(REFRESH_TOKEN_NAME);\r\n  return token ? String(token) : null;\r\n};\r\n\r\n/**\r\n * Set the access token in cookies\r\n * @param token The access token to set\r\n * @param expiresIn Expiration time in seconds\r\n */\r\nexport const setAccessToken = (token: string, expiresIn: number = 3600): void => {\r\n  const expirationDate = new Date();\r\n  expirationDate.setSeconds(expirationDate.getSeconds() + expiresIn);\r\n\r\n  setCookie(ACCESS_TOKEN_NAME, token, {\r\n    expires: expirationDate,\r\n    path: \"/\",\r\n    secure: process.env.NODE_ENV === \"production\",\r\n    sameSite: \"strict\",\r\n  });\r\n};\r\n\r\n/**\r\n * Set the refresh token in cookies\r\n * @param token The refresh token to set\r\n * @param expiresIn Expiration time in seconds (default: 30 days)\r\n */\r\nexport const setRefreshToken = (token: string, expiresIn: number = 30 * 24 * 60 * 60): void => {\r\n  const expirationDate = new Date();\r\n  expirationDate.setSeconds(expirationDate.getSeconds() + expiresIn);\r\n\r\n  setCookie(REFRESH_TOKEN_NAME, token, {\r\n    expires: expirationDate,\r\n    path: \"/\",\r\n    secure: process.env.NODE_ENV === \"production\",\r\n    sameSite: \"strict\",\r\n  });\r\n};\r\n\r\n/**\r\n * Clear all authentication cookies\r\n */\r\nexport const clearAuthCookies = async (): Promise<void> => {\r\n  // Clear access token with default path\r\n  deleteCookie(ACCESS_TOKEN_NAME, { path: \"/\" });\r\n\r\n  // Clear refresh token with both possible paths to ensure it's removed\r\n  deleteCookie(REFRESH_TOKEN_NAME, { path: \"/\" });\r\n  deleteCookie(REFRESH_TOKEN_NAME, { path: \"/api/auth/refresh\" });\r\n\r\n  console.log(\"Auth cookies cleared with specific paths\");\r\n};\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;;;;AAC3B;;AAGO,MAAM,oBAAoB;AAC1B,MAAM,qBAAqB;AAM3B,MAAM,iBAAiB;IAC5B,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE;IACxB,OAAO,QAAQ,OAAO,SAAS;AACjC;AAMO,MAAM,kBAAkB;IAC7B,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE;IACxB,OAAO,QAAQ,OAAO,SAAS;AACjC;AAOO,MAAM,iBAAiB,CAAC,OAAe,YAAoB,IAAI;IACpE,MAAM,iBAAiB,IAAI;IAC3B,eAAe,UAAU,CAAC,eAAe,UAAU,KAAK;IAExD,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB,OAAO;QAClC,SAAS;QACT,MAAM;QACN,QAAQ,oDAAyB;QACjC,UAAU;IACZ;AACF;AAOO,MAAM,kBAAkB,CAAC,OAAe,YAAoB,KAAK,KAAK,KAAK,EAAE;IAClF,MAAM,iBAAiB,IAAI;IAC3B,eAAe,UAAU,CAAC,eAAe,UAAU,KAAK;IAExD,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB,OAAO;QACnC,SAAS;QACT,MAAM;QACN,QAAQ,oDAAyB;QACjC,UAAU;IACZ;AACF;AAKO,MAAM,mBAAmB;IAC9B,uCAAuC;IACvC,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;QAAE,MAAM;IAAI;IAE5C,sEAAsE;IACtE,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB;QAAE,MAAM;IAAI;IAC7C,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB;QAAE,MAAM;IAAoB;IAE7D,QAAQ,GAAG,CAAC;AACd", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/clientCookies.ts"], "sourcesContent": ["/**\r\n * Client-side cookie utilities\r\n * These functions can be used in client components to get/set cookies in the browser\r\n */\r\n\r\nimport Cookies from \"js-cookie\";\r\n\r\n// Get access token from client-side cookie\r\nexport const getClientAccessToken = (): string => {\r\n  return Cookies.get(\"accessToken\") || \"\";\r\n};\r\n\r\n// Get refresh token from client-side cookie\r\n// Note: This will always return null now that refresh token is HTTP-only\r\nexport const getClientRefreshToken = (): string | null => {\r\n  // The refresh token is now HTTP-only and not accessible from client-side JavaScript\r\n  // This function is kept for backward compatibility but will always return null\r\n  return null;\r\n};\r\n\r\n// Check if access token exists in client-side cookies\r\nexport const checkClientAccessToken = (): boolean => {\r\n  const token = Cookies.get(\"accessToken\");\r\n  console.log(\"Client-side access token check:\", !!token);\r\n  return !!token;\r\n};\r\n\r\n// Set auth cookies on the client side\r\nexport const setClientAuthCookies = (\r\n  accessToken: string,\r\n  refreshToken: string | null,\r\n  accessTokenAge: number,\r\n  refreshTokenAge: number | null,\r\n): void => {\r\n  // Set access token cookie\r\n  // Note: The refresh token is handled by the server-side HTTP-only cookie\r\n  // and should not be set on the client side\r\n  Cookies.set(\"accessToken\", accessToken, {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    secure: true,\r\n    sameSite: \"lax\", // Changed from \"none\" to \"lax\" for better CSRF protection\r\n    expires: accessTokenAge / (60 * 60 * 24), // Convert seconds to days\r\n  });\r\n\r\n  // We no longer set the refresh token on the client side\r\n  // as it's now handled by an HTTP-only cookie set by the server\r\n  // This improves security by preventing client-side JavaScript from accessing the refresh token\r\n};\r\n\r\n// Clear auth cookies on the client side\r\nexport const clearClientAuthCookies = (): void => {\r\n  // Clear access token from client-side cookie\r\n  Cookies.remove(\"accessToken\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n  });\r\n\r\n  // Attempt to clear refresh token with both possible paths\r\n  // Even though it's HTTP-only, we try to clear it from client-side as a fallback\r\n  Cookies.remove(\"refreshToken\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n  });\r\n\r\n  Cookies.remove(\"refreshToken\", {\r\n    path: \"/api/auth/refresh\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n  });\r\n\r\n  console.log(\"Client-side cookie clearing attempted for both tokens\");\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAED;;AAGO,MAAM,uBAAuB;IAClC,OAAO,uJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB;AACvC;AAIO,MAAM,wBAAwB;IACnC,oFAAoF;IACpF,+EAA+E;IAC/E,OAAO;AACT;AAGO,MAAM,yBAAyB;IACpC,MAAM,QAAQ,uJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,QAAQ,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACjD,OAAO,CAAC,CAAC;AACX;AAGO,MAAM,uBAAuB,CAClC,aACA,cACA,gBACA;IAEA,0BAA0B;IAC1B,yEAAyE;IACzE,2CAA2C;IAC3C,uJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,eAAe,aAAa;QACtC,MAAM;QACN,MAAM;QACN,QAAQ;QACR,UAAU;QACV,SAAS,iBAAiB,CAAC,KAAK,KAAK,EAAE;IACzC;AAEA,wDAAwD;AACxD,+DAA+D;AAC/D,+FAA+F;AACjG;AAGO,MAAM,yBAAyB;IACpC,6CAA6C;IAC7C,uJAAA,CAAA,UAAO,CAAC,MAAM,CAAC,eAAe;QAC5B,MAAM;QACN,MAAM;IACR;IAEA,0DAA0D;IAC1D,gFAAgF;IAChF,uJAAA,CAAA,UAAO,CAAC,MAAM,CAAC,gBAAgB;QAC7B,MAAM;QACN,MAAM;IACR;IAEA,uJAAA,CAAA,UAAO,CAAC,MAAM,CAAC,gBAAgB;QAC7B,MAAM;QACN,MAAM;IACR;IAEA,QAAQ,GAAG,CAAC;AACd", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/shared/routes.ts"], "sourcesContent": ["/**\r\n * Shared route definitions for the application\r\n * Matches the implementation in ruh-app-fe for consistency.\r\n */\r\n\r\n// Authentication routes\r\nexport const loginRoute = \"/login\";\r\nexport const signupRoute = \"/signup\";\r\nexport const verifyEmailRoute = \"/verify-email\";\r\nexport const updatePasswordRoute = \"/reset-password\";\r\nexport const authRoute = `${process.env.NEXT_PUBLIC_AUTHENTICATION_URL}?redirect_url=${process.env.NEXT_PUBLIC_APP_URL}`;\r\n\r\n// Dashboard routes\r\nexport const homeRoute = \"/workflows\"; // Updated to point to workflows instead of /home\r\nexport const workflowsRoute = \"/workflows\";\r\nexport const settingsRoute = \"/settings\";\r\nexport const credentialsRoute = \"/credentials\";\r\n\r\n// Public routes\r\nexport const aboutRoute = \"/about\";\r\nexport const contactRoute = \"/contact\";\r\n\r\n// Define public routes that don't require authentication\r\nexport const publicRoutes = [\r\n  loginRoute,\r\n  signupRoute,\r\n  verifyEmailRoute,\r\n  updatePasswordRoute,\r\n  aboutRoute,\r\n  contactRoute,\r\n];\r\n\r\n// Define protected routes that require authentication\r\nexport const protectedRoutes = [workflowsRoute, workflowsRoute, settingsRoute, credentialsRoute];\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;;;;AACjB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,mBAAmB;AACzB,MAAM,sBAAsB;AAC5B,MAAM,YAAY,8DAA8C,cAAc,8DAAmC;AAGjH,MAAM,YAAY,cAAc,iDAAiD;AACjF,MAAM,iBAAiB;AACvB,MAAM,gBAAgB;AACtB,MAAM,mBAAmB;AAGzB,MAAM,aAAa;AACnB,MAAM,eAAe;AAGrB,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,kBAAkB;IAAC;IAAgB;IAAgB;IAAe;CAAiB", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/hooks/use-user.ts"], "sourcesContent": ["// src/hooks/use-user.ts\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\n\r\nexport interface User {\r\n  id: string;\r\n  email: string;\r\n  name?: string;\r\n  role?: string;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\ninterface UserState {\r\n  user: User | null;\r\n  setUser: (user: User) => void;\r\n  clearUser: () => void;\r\n  isAuthenticated: () => boolean;\r\n}\r\n\r\nexport const useUserStore = create<UserState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      user: null,\r\n      setUser: (user: User) => set({ user: { ...user, isAuthenticated: true } }),\r\n      clearUser: () => set({ user: null }),\r\n      isAuthenticated: () => !!get().user?.isAuthenticated,\r\n    }),\r\n    {\r\n      name: \"user-storage\",\r\n    },\r\n  ),\r\n);\r\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;AACxB;AACA;;;AAiBO,MAAM,eAAe,CAAA,GAAA,0IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,SAAS,CAAC,OAAe,IAAI;gBAAE,MAAM;oBAAE,GAAG,IAAI;oBAAE,iBAAiB;gBAAK;YAAE;QACxE,WAAW,IAAM,IAAI;gBAAE,MAAM;YAAK;QAClC,iBAAiB,IAAM,CAAC,CAAC,MAAM,IAAI,EAAE;IACvC,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/utils/axios.ts"], "sourcesContent": ["import axios, { AxiosError, AxiosRequestConfig, AxiosInstance } from \"axios\";\r\nimport { getAccessToken, clearAuthCookies } from \"./authCookies\";\r\nimport { getClientAccessToken, clearClientAuthCookies } from \"@/lib/clientCookies\";\r\nimport { loginRoute } from \"@/shared/routes\";\r\nimport { useUserStore } from \"@/hooks/use-user\";\r\n\r\n// Helper function to clear user store\r\nconst clearUserStore = () => {\r\n  useUserStore.getState().clearUser();\r\n};\r\n\r\n// Configuration interface for axios instances\r\nexport interface AxiosInstanceConfig {\r\n  baseURL?: string;\r\n  withCredentials?: boolean;\r\n  enableTokenRefresh?: boolean;\r\n  enableClientSideToken?: boolean;\r\n  customHeaders?: Record<string, string>;\r\n}\r\n\r\n// Helper function to get appropriate token based on environment\r\nconst getAppropriateToken = async (enableClientSide: boolean = false): Promise<string | null> => {\r\n  if (enableClientSide && typeof window !== \"undefined\") {\r\n    // Client-side\r\n    return getClientAccessToken();\r\n  } else {\r\n    // Server-side or fallback\r\n    return await getAccessToken();\r\n  }\r\n};\r\n\r\n// Create request interceptor\r\nconst createRequestInterceptor = (config: AxiosInstanceConfig = {}) => {\r\n  return async (requestConfig: any) => {\r\n    // Ensure headers object exists\r\n    if (!requestConfig.headers) {\r\n      requestConfig.headers = {};\r\n    }\r\n\r\n    const token = await getAppropriateToken(config.enableClientSideToken);\r\n    if (token) {\r\n      requestConfig.headers.Authorization = `Bearer ${token}`;\r\n      console.log(`[DEBUG] Added Authorization header with token (length: ${token.length})`);\r\n    } else {\r\n      console.log(`[DEBUG] No token available for request to ${requestConfig.url}`);\r\n    }\r\n\r\n    // Add common headers\r\n    requestConfig.headers[\"ngrok-skip-browser-warning\"] = \"true\";\r\n\r\n    // Add custom headers if provided\r\n    if (config.customHeaders) {\r\n      Object.assign(requestConfig.headers, config.customHeaders);\r\n    }\r\n\r\n    return requestConfig;\r\n  };\r\n};\r\n\r\n// Create response interceptor\r\nconst createResponseInterceptor = (instance: AxiosInstance, config: AxiosInstanceConfig = {}) => {\r\n  return async (error: AxiosError) => {\r\n    const originalRequest = error.config as AxiosRequestConfig & {\r\n      _retry?: boolean;\r\n    };\r\n\r\n    // Only handle token refresh if enabled\r\n    if (!config.enableTokenRefresh) {\r\n      return Promise.reject(error);\r\n    }\r\n\r\n    // Check if the error is due to an expired token (401 Unauthorized or 403 Forbidden)\r\n    if (\r\n      (error.response?.status === 401 || error.response?.status === 403) &&\r\n      !originalRequest._retry\r\n    ) {\r\n      // Mark this request as retried to prevent infinite loops\r\n      originalRequest._retry = true;\r\n\r\n      try {\r\n        // Call the refresh token endpoint using a fresh axios instance to avoid infinite loops\r\n        // This endpoint will use the HTTP-only refresh token cookie automatically\r\n        const refreshInstance = axios.create();\r\n        const response = await refreshInstance.post(\"/api/auth/refresh\");\r\n\r\n        if (response.data.success && response.data.accessToken) {\r\n          // Update the authorization header with the new token\r\n          originalRequest.headers = {\r\n            ...originalRequest.headers,\r\n            Authorization: `Bearer ${response.data.accessToken}`,\r\n          };\r\n\r\n          // Retry the original request with the new token\r\n          return instance(originalRequest);\r\n        } else {\r\n          // Token refresh failed\r\n          await clearAuthCookies();\r\n          clearUserStore();\r\n          if (typeof window !== \"undefined\") {\r\n            window.location.href = loginRoute;\r\n          }\r\n          return Promise.reject(new Error(\"Token refresh failed\"));\r\n        }\r\n      } catch (refreshError) {\r\n        // Clear cookies and redirect to login on refresh error\r\n        await clearAuthCookies();\r\n        clearUserStore();\r\n        if (typeof window !== \"undefined\") {\r\n          window.location.href = loginRoute;\r\n        }\r\n        return Promise.reject(refreshError);\r\n      }\r\n    }\r\n\r\n    // For other errors, just reject the promise\r\n    return Promise.reject(error);\r\n  };\r\n};\r\n\r\n// Create a centralized axios instance factory\r\nexport const createAxiosInstance = (config: AxiosInstanceConfig = {}): AxiosInstance => {\r\n  const instance = axios.create({\r\n    baseURL: config.baseURL || process.env.NEXT_PUBLIC_API_URL,\r\n    withCredentials: config.withCredentials ?? false,\r\n  });\r\n\r\n  // Add request interceptor\r\n  instance.interceptors.request.use(createRequestInterceptor(config), (error: any) => {\r\n    return Promise.reject(\r\n      new Error(`Request interceptor error: ${error.message || \"Unknown error\"}`),\r\n    );\r\n  });\r\n\r\n  // Add response interceptor\r\n  instance.interceptors.response.use(\r\n    (response) => response,\r\n    createResponseInterceptor(instance, config),\r\n  );\r\n\r\n  return instance;\r\n};\r\n\r\n// Main API instance with full token refresh capabilities\r\nconst api = createAxiosInstance({\r\n  enableTokenRefresh: true,\r\n  enableClientSideToken: true,\r\n});\r\n\r\n// Workflow API instance\r\nexport const workflowApi = createAxiosInstance({\r\n  enableTokenRefresh: false,\r\n  enableClientSideToken: true,\r\n});\r\n\r\n// Auth API instance\r\nexport const authApi = createAxiosInstance({\r\n  enableTokenRefresh: true,\r\n  enableClientSideToken: true,\r\n  withCredentials: true,\r\n});\r\n\r\n// External API instance (for external URLs without auth)\r\nexport const externalApi = axios.create();\r\n\r\n// Centralized logout function\r\nexport const logout = async (): Promise<void> => {\r\n  try {\r\n    // Clear all auth cookies (both access and refresh tokens)\r\n    await clearAuthCookies();\r\n\r\n    // Clear user store\r\n    clearUserStore();\r\n\r\n    // Redirect to login page\r\n    if (typeof window !== \"undefined\") {\r\n      window.location.href = loginRoute;\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error during logout:\", error);\r\n\r\n    // Even if there's an error, try to clear everything\r\n    await clearAuthCookies();\r\n    clearUserStore();\r\n\r\n    if (typeof window !== \"undefined\") {\r\n      window.location.href = loginRoute;\r\n    }\r\n  }\r\n};\r\n\r\nexport default api;\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,sCAAsC;AACtC,MAAM,iBAAiB;IACrB,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS;AACnC;AAWA,gEAAgE;AAChE,MAAM,sBAAsB,OAAO,mBAA4B,KAAK;IAClE,uCAAuD;;IAGvD,OAAO;QACL,0BAA0B;QAC1B,OAAO,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAC5B;AACF;AAEA,6BAA6B;AAC7B,MAAM,2BAA2B,CAAC,SAA8B,CAAC,CAAC;IAChE,OAAO,OAAO;QACZ,+BAA+B;QAC/B,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,cAAc,OAAO,GAAG,CAAC;QAC3B;QAEA,MAAM,QAAQ,MAAM,oBAAoB,OAAO,qBAAqB;QACpE,IAAI,OAAO;YACT,cAAc,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YACvD,QAAQ,GAAG,CAAC,CAAC,uDAAuD,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC;QACvF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,cAAc,GAAG,EAAE;QAC9E;QAEA,qBAAqB;QACrB,cAAc,OAAO,CAAC,6BAA6B,GAAG;QAEtD,iCAAiC;QACjC,IAAI,OAAO,aAAa,EAAE;YACxB,OAAO,MAAM,CAAC,cAAc,OAAO,EAAE,OAAO,aAAa;QAC3D;QAEA,OAAO;IACT;AACF;AAEA,8BAA8B;AAC9B,MAAM,4BAA4B,CAAC,UAAyB,SAA8B,CAAC,CAAC;IAC1F,OAAO,OAAO;QACZ,MAAM,kBAAkB,MAAM,MAAM;QAIpC,uCAAuC;QACvC,IAAI,CAAC,OAAO,kBAAkB,EAAE;YAC9B,OAAO,QAAQ,MAAM,CAAC;QACxB;QAEA,oFAAoF;QACpF,IACE,CAAC,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,QAAQ,EAAE,WAAW,GAAG,KACjE,CAAC,gBAAgB,MAAM,EACvB;YACA,yDAAyD;YACzD,gBAAgB,MAAM,GAAG;YAEzB,IAAI;gBACF,uFAAuF;gBACvF,0EAA0E;gBAC1E,MAAM,kBAAkB,uIAAA,CAAA,UAAK,CAAC,MAAM;gBACpC,MAAM,WAAW,MAAM,gBAAgB,IAAI,CAAC;gBAE5C,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,WAAW,EAAE;oBACtD,qDAAqD;oBACrD,gBAAgB,OAAO,GAAG;wBACxB,GAAG,gBAAgB,OAAO;wBAC1B,eAAe,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,WAAW,EAAE;oBACtD;oBAEA,gDAAgD;oBAChD,OAAO,SAAS;gBAClB,OAAO;oBACL,uBAAuB;oBACvB,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;oBACrB;oBACA,uCAAmC;;oBAEnC;oBACA,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;gBAClC;YACF,EAAE,OAAO,cAAc;gBACrB,uDAAuD;gBACvD,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;gBACrB;gBACA,uCAAmC;;gBAEnC;gBACA,OAAO,QAAQ,MAAM,CAAC;YACxB;QACF;QAEA,4CAA4C;QAC5C,OAAO,QAAQ,MAAM,CAAC;IACxB;AACF;AAGO,MAAM,sBAAsB,CAAC,SAA8B,CAAC,CAAC;IAClE,MAAM,WAAW,uIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAC5B,SAAS,OAAO,OAAO;QACvB,iBAAiB,OAAO,eAAe,IAAI;IAC7C;IAEA,0BAA0B;IAC1B,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC;QACnE,OAAO,QAAQ,MAAM,CACnB,IAAI,MAAM,CAAC,2BAA2B,EAAE,MAAM,OAAO,IAAI,iBAAiB;IAE9E;IAEA,2BAA2B;IAC3B,SAAS,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC,WAAa,UACd,0BAA0B,UAAU;IAGtC,OAAO;AACT;AAEA,yDAAyD;AACzD,MAAM,MAAM,oBAAoB;IAC9B,oBAAoB;IACpB,uBAAuB;AACzB;AAGO,MAAM,cAAc,oBAAoB;IAC7C,oBAAoB;IACpB,uBAAuB;AACzB;AAGO,MAAM,UAAU,oBAAoB;IACzC,oBAAoB;IACpB,uBAAuB;IACvB,iBAAiB;AACnB;AAGO,MAAM,cAAc,uIAAA,CAAA,UAAK,CAAC,MAAM;AAGhC,MAAM,SAAS;IACpB,IAAI;QACF,0DAA0D;QAC1D,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;QAErB,mBAAmB;QACnB;QAEA,yBAAyB;QACzB,uCAAmC;;QAEnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,oDAAoD;QACpD,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;QACrB;QAEA,uCAAmC;;QAEnC;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/cookies.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\n\r\nexport const getAccessToken = async () => {\r\n  const cookieStore = await cookies();\r\n  const accessToken = cookieStore.get(\"accessToken\");\r\n  if (accessToken) {\r\n    return accessToken.value;\r\n  } else {\r\n    return \"\";\r\n  }\r\n};\r\n\r\nexport const getRefreshToken = async () => {\r\n  const cookieStore = await cookies();\r\n  const refreshToken = cookieStore.get(\"refreshToken\");\r\n  return refreshToken?.value || null;\r\n};\r\n\r\nexport const checkAccessToken = async () => {\r\n  const cookieStore = await cookies();\r\n  const tokenCookie = cookieStore.get(\"accessToken\");\r\n  return Boolean(tokenCookie?.value);\r\n};\r\n\r\nexport const setAuthCookies = async (\r\n  accessToken: string,\r\n  refreshToken: string | null,\r\n  accessTokenAge: number,\r\n  refreshTokenAge: number | null,\r\n) => {\r\n  const cookieStore = await cookies();\r\n\r\n  // Set access token as a non-HttpOnly cookie (accessible to JavaScript)\r\n  // This is for client-side access to the token for API calls\r\n  cookieStore.set(\"accessToken\", accessToken, {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: false, // Accessible to JavaScript\r\n    sameSite: \"lax\", // Changed from \"none\" to \"lax\" for better CSRF protection\r\n    secure: true,\r\n    maxAge: accessTokenAge,\r\n  });\r\n\r\n  // Set a secure HTTP-only cookie for the refresh token\r\n  // This provides better security as it's not accessible to JavaScript\r\n  if (refreshToken && refreshTokenAge) {\r\n    cookieStore.set(\"refreshToken\", refreshToken, {\r\n      path: \"/api/auth/refresh\", // Restrict to refresh endpoint only\r\n      domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n      httpOnly: true, // Not accessible to JavaScript\r\n      sameSite: \"lax\", // Changed from \"none\" to \"lax\" for better CSRF protection\r\n      secure: true,\r\n      maxAge: refreshTokenAge,\r\n    });\r\n  }\r\n};\r\n\r\nexport const clearAuthCookies = async () => {\r\n  \"use server\";\r\n\r\n  const cookieStore = await cookies();\r\n\r\n  // Clear access token cookie\r\n  cookieStore.set(\"accessToken\", \"\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: false, // Match the setting used when creating the cookie\r\n    secure: true,\r\n    sameSite: \"lax\", // Match the setting used when creating the cookie\r\n    maxAge: 0,\r\n    expires: new Date(0),\r\n  });\r\n\r\n  // Clear refresh token cookie with the specific path used when setting it\r\n  cookieStore.set(\"refreshToken\", \"\", {\r\n    path: \"/api/auth/refresh\", // Match the path restriction used when creating the cookie\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: true, // Match the setting used when creating the cookie\r\n    secure: true,\r\n    sameSite: \"lax\", // Match the setting used when creating the cookie\r\n    maxAge: 0,\r\n    expires: new Date(0),\r\n  });\r\n\r\n  // Also clear refresh token with root path as a fallback\r\n  cookieStore.set(\"refreshToken\", \"\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: true,\r\n    secure: true,\r\n    sameSite: \"lax\",\r\n    maxAge: 0,\r\n    expires: new Date(0),\r\n  });\r\n\r\n  console.log(\"Server-side auth cookies cleared with multiple paths\");\r\n};\r\n\r\nexport const setRefreshingTokenCookie = async () => {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set(\"refreshingToken\", \"true\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: false, // Must be false to be accessible from JavaScript\r\n    sameSite: \"none\",\r\n    maxAge: 60, // 1 minute should be enough to handle the refresh\r\n  });\r\n};\r\n\r\nexport const clearRefreshingTokenCookie = async () => {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set(\"refreshingToken\", \"\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    maxAge: 0,\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;;;;;AAEO,MAAM,uCAAY,GAAZ,iBAAiB;IAC5B,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc,YAAY,GAAG,CAAC;IACpC,IAAI,aAAa;QACf,OAAO,YAAY,KAAK;IAC1B,OAAO;QACL,OAAO;IACT;AACF;AAEO,MAAM,uCAAa,GAAb,kBAAkB;IAC7B,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,OAAO,cAAc,SAAS;AAChC;AAEO,MAAM,uCAAc,GAAd,mBAAmB;IAC9B,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc,YAAY,GAAG,CAAC;IACpC,OAAO,QAAQ,aAAa;AAC9B;AAEO,MAAM,uCAAY,GAAZ,iBAAiB,OAC5B,aACA,cACA,gBACA;IAEA,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,uEAAuE;IACvE,4DAA4D;IAC5D,YAAY,GAAG,CAAC,eAAe,aAAa;QAC1C,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IAEA,sDAAsD;IACtD,qEAAqE;IACrE,IAAI,gBAAgB,iBAAiB;QACnC,YAAY,GAAG,CAAC,gBAAgB,cAAc;YAC5C,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,QAAQ;YACR,QAAQ;QACV;IACF;AACF;AAEO,MAAM,uCAAc,GAAd,mBAAmB;IAG9B,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,4BAA4B;IAC5B,YAAY,GAAG,CAAC,eAAe,IAAI;QACjC,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS,IAAI,KAAK;IACpB;IAEA,yEAAyE;IACzE,YAAY,GAAG,CAAC,gBAAgB,IAAI;QAClC,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS,IAAI,KAAK;IACpB;IAEA,wDAAwD;IACxD,YAAY,GAAG,CAAC,gBAAgB,IAAI;QAClC,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS,IAAI,KAAK;IACpB;IAEA,QAAQ,GAAG,CAAC;AACd;AAEO,MAAM,uCAAsB,GAAtB,2BAA2B;IACtC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC,mBAAmB,QAAQ;QACzC,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,QAAQ;IACV;AACF;AAEO,MAAM,uCAAwB,GAAxB,6BAA6B;IACxC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC,mBAAmB,IAAI;QACrC,MAAM;QACN,MAAM;QACN,QAAQ;IACV;AACF;;;IAlHa;IAUA;IAMA;IAMA;IAiCA;IAyCA;IAWA;;AA3GA,iPAAA;AAUA,iPAAA;AAMA,iPAAA;AAMA,iPAAA;AAiCA,iPAAA;AAyCA,iPAAA;AAWA,iPAAA", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/store/userStore.ts"], "sourcesContent": ["/**\r\n * User store for authentication state management\r\n * Matches the implementation in ruh-app-fe for consistency.\r\n */\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\n\r\n// Define the shape of the user data\r\nexport interface User {\r\n  fullName?: string | null;\r\n  email?: string | null;\r\n  company?: string | null;\r\n  department?: string | null;\r\n  jobRole?: string | null;\r\n  accessToken?: string | null;\r\n}\r\n\r\n// Define the state structure\r\ninterface UserState {\r\n  user: User | null;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\n// Define the actions available on the store\r\ninterface UserActions {\r\n  setUser: (userData: User | null) => void;\r\n  clearUser: () => void;\r\n  logout: () => Promise<void>;\r\n}\r\n\r\n// Create the Zustand store\r\nexport const useUserStore = create<UserState & UserActions>()(\r\n  persist(\r\n    (set, get) => ({\r\n      // Initial state\r\n      user: null,\r\n      isAuthenticated: false,\r\n\r\n      // Actions\r\n      setUser: (userData) =>\r\n        set((state) => ({\r\n          // Merge existing user data with new data if user exists\r\n          user: state.user ? { ...state.user, ...userData } : userData,\r\n          // Update isAuthenticated based on presence of accessToken\r\n          isAuthenticated: userData?.accessToken ? true : false,\r\n        })),\r\n\r\n      clearUser: () =>\r\n        set({\r\n          user: null,\r\n          isAuthenticated: false,\r\n        }),\r\n\r\n      // Logout action that calls the authApi logout method\r\n      logout: async () => {\r\n        try {\r\n          // Import dynamically to avoid circular dependencies\r\n          const { authApi } = await import(\"@/lib/authApi\");\r\n          await authApi.logout();\r\n        } catch (error) {\r\n          console.error(\"Error during logout:\", error);\r\n          // Clear user state even if logout API fails\r\n          get().clearUser();\r\n        }\r\n      },\r\n    }),\r\n    {\r\n      name: \"user-storage\", // Unique name for the storage key\r\n      // You can also specify storage: () => sessionStorage or other storage APIs\r\n    },\r\n  ),\r\n);\r\n\r\nexport default useUserStore;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;;;AA0BO,MAAM,eAAe,CAAA,GAAA,0IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QAEjB,UAAU;QACV,SAAS,CAAC,WACR,IAAI,CAAC,QAAU,CAAC;oBACd,wDAAwD;oBACxD,MAAM,MAAM,IAAI,GAAG;wBAAE,GAAG,MAAM,IAAI;wBAAE,GAAG,QAAQ;oBAAC,IAAI;oBACpD,0DAA0D;oBAC1D,iBAAiB,UAAU,cAAc,OAAO;gBAClD,CAAC;QAEH,WAAW,IACT,IAAI;gBACF,MAAM;gBACN,iBAAiB;YACnB;QAEF,qDAAqD;QACrD,QAAQ;YACN,IAAI;gBACF,oDAAoD;gBACpD,MAAM,EAAE,OAAO,EAAE,GAAG;gBACpB,MAAM,QAAQ,MAAM;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,4CAA4C;gBAC5C,MAAM,SAAS;YACjB;QACF;IACF,CAAC,GACD;IACE,MAAM;AAER;uCAIW", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/apiConfig.ts"], "sourcesContent": ["/**\r\n * API Configuration\r\n *\r\n * This file centralizes all API URL configurations to ensure consistency\r\n * across the application.\r\n *\r\n * Note: The API_BASE_URL already includes '/api/v1' from the environment variable\r\n * (e.g., https://app-dev.rapidinnovation.dev/api/v1)\r\n */\r\n\r\n// Base API URL from environment variable (already includes /api/v1)\r\nexport const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL!;\r\n\r\n// Auth-specific endpoints (don't add /api/v1 again)\r\nexport const AUTH_API_URL = `${API_BASE_URL}/auth`;\r\n\r\n// Workflow-specific endpoints (don't add /api/v1 again)\r\nexport const WORKFLOWS_API_URL = `${API_BASE_URL}/workflows`;\r\n\r\n// MCP-specific endpoints (don't add /api/v1 again)\r\nexport const MCPS_API_URL = `${API_BASE_URL}/mcps`;\r\n\r\n// Workflow execution API URL (separate service)\r\nexport const WORKFLOW_EXECUTION_URL = process.env.NEXT_PUBLIC_WORKFLOW_EXECUTION_URL!;\r\n\r\n// API endpoints\r\nexport const API_ENDPOINTS = {\r\n  // Auth endpoints\r\n  AUTH: {\r\n    LOGIN: `${AUTH_API_URL}/login`,\r\n    REGISTER: `${AUTH_API_URL}/register`,\r\n    LOGOUT: `${AUTH_API_URL}/logout`,\r\n    REFRESH: `${AUTH_API_URL}/refresh`,\r\n    FORGOT_PASSWORD: `${AUTH_API_URL}/forgot-password`,\r\n    RESET_PASSWORD: `${AUTH_API_URL}/reset-password`,\r\n    VERIFY_EMAIL: `${AUTH_API_URL}/verify-email`,\r\n    VERIFY_EMAIL_OTP: `${AUTH_API_URL}/verify-email-otp`,\r\n    UPDATE_PASSWORD: `${AUTH_API_URL}/update-password`,\r\n  },\r\n\r\n  // Workflow endpoints\r\n  WORKFLOWS: {\r\n    LIST: `${WORKFLOWS_API_URL}`,\r\n    CREATE: `${WORKFLOWS_API_URL}`,\r\n    GET: (id: string) => `${WORKFLOWS_API_URL}/${id}`,\r\n    UPDATE: (id: string) => `${WORKFLOWS_API_URL}/${id}`,\r\n    DELETE: (id: string) => `${WORKFLOWS_API_URL}/${id}`,\r\n    EXECUTE: (id: string) => `${WORKFLOWS_API_URL}/${id}/execute`,\r\n  },\r\n\r\n  // MCP endpoints\r\n  MCPS: {\r\n    LIST: `${MCPS_API_URL}`,\r\n    GET: (id: string) => `${MCPS_API_URL}/${id}`,\r\n  },\r\n\r\n  // Workflow Execution endpoints (separate service)\r\n  WORKFLOW_EXECUTION: {\r\n    EXECUTE: `${WORKFLOW_EXECUTION_URL}/workflow-execute/execute`,\r\n    APPROVE: `${WORKFLOW_EXECUTION_URL}/workflow-execute/approve`,\r\n    STREAM: `${WORKFLOW_EXECUTION_URL}/workflow-execute/stream`,\r\n  },\r\n\r\n  // Other API endpoints can be added here\r\n};\r\n\r\n// Export default for easier imports\r\nexport default {\r\n  API_BASE_URL,\r\n  AUTH_API_URL,\r\n  WORKFLOWS_API_URL,\r\n  MCPS_API_URL,\r\n  WORKFLOW_EXECUTION_URL,\r\n  API_ENDPOINTS,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED,oEAAoE;;;;;;;;;;AAC7D,MAAM;AAGN,MAAM,eAAe,GAAG,aAAa,KAAK,CAAC;AAG3C,MAAM,oBAAoB,GAAG,aAAa,UAAU,CAAC;AAGrD,MAAM,eAAe,GAAG,aAAa,KAAK,CAAC;AAG3C,MAAM;AAGN,MAAM,gBAAgB;IAC3B,iBAAiB;IACjB,MAAM;QACJ,OAAO,GAAG,aAAa,MAAM,CAAC;QAC9B,UAAU,GAAG,aAAa,SAAS,CAAC;QACpC,QAAQ,GAAG,aAAa,OAAO,CAAC;QAChC,SAAS,GAAG,aAAa,QAAQ,CAAC;QAClC,iBAAiB,GAAG,aAAa,gBAAgB,CAAC;QAClD,gBAAgB,GAAG,aAAa,eAAe,CAAC;QAChD,cAAc,GAAG,aAAa,aAAa,CAAC;QAC5C,kBAAkB,GAAG,aAAa,iBAAiB,CAAC;QACpD,iBAAiB,GAAG,aAAa,gBAAgB,CAAC;IACpD;IAEA,qBAAqB;IACrB,WAAW;QACT,MAAM,GAAG,mBAAmB;QAC5B,QAAQ,GAAG,mBAAmB;QAC9B,KAAK,CAAC,KAAe,GAAG,kBAAkB,CAAC,EAAE,IAAI;QACjD,QAAQ,CAAC,KAAe,GAAG,kBAAkB,CAAC,EAAE,IAAI;QACpD,QAAQ,CAAC,KAAe,GAAG,kBAAkB,CAAC,EAAE,IAAI;QACpD,SAAS,CAAC,KAAe,GAAG,kBAAkB,CAAC,EAAE,GAAG,QAAQ,CAAC;IAC/D;IAEA,gBAAgB;IAChB,MAAM;QACJ,MAAM,GAAG,cAAc;QACvB,KAAK,CAAC,KAAe,GAAG,aAAa,CAAC,EAAE,IAAI;IAC9C;IAEA,kDAAkD;IAClD,oBAAoB;QAClB,SAAS,GAAG,uBAAuB,yBAAyB,CAAC;QAC7D,SAAS,GAAG,uBAAuB,yBAAyB,CAAC;QAC7D,QAAQ,GAAG,uBAAuB,wBAAwB,CAAC;IAC7D;AAGF;uCAGe;IACb;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/authApi.ts"], "sourcesContent": ["/**\r\n * Authentication API client for workflow-builder-poc\r\n * This module provides functions for authentication operations.\r\n * Matches the implementation in ruh-app-fe for consistency.\r\n */\r\n\r\nimport { authApi as centralizedAuth<PERSON><PERSON> } from \"@/utils/axios\";\r\nimport { clearAuthCookies, getAccessToken, setAuthCookies } from \"./cookies\";\r\nimport {\r\n  getClientAccessToken,\r\n  checkClientAccessToken,\r\n  setClientAuthCookies,\r\n  clearClientAuthCookies,\r\n} from \"./clientCookies\";\r\nimport { LoginType, SignupType, ResetPasswordType } from \"./schemas/auth\";\r\nimport { useUserStore } from \"@/store/userStore\";\r\nimport { LoginResponse, SignupResponse, LoginResult, TokenResponse } from \"@/shared/interfaces\";\r\nimport { API_BASE_URL, API_ENDPOINTS } from \"./apiConfig\";\r\nimport { workflowsRoute, loginRoute } from \"@/shared/routes\";\r\n/**\r\n * Checks if a user has completed the onboarding process\r\n * by verifying they have both department and jobRole fields.\r\n *\r\n * @returns {Promise<boolean>} True if onboarding is complete, false otherwise\r\n */\r\nexport async function hasCompletedOnboarding(): Promise<boolean> {\r\n  try {\r\n    const userDetails = await authApi.getCurrentUser();\r\n    return Boolean(userDetails?.department && userDetails?.jobRole);\r\n  } catch (error: any) {\r\n    // Check if this request has already been through the axios interceptor\r\n    const originalRequest = error.config as any;\r\n    if (originalRequest && originalRequest._retry) {\r\n      // This request has already been through the interceptor and still failed\r\n      // This means token refresh failed, so we should redirect to login\r\n      throw error;\r\n    }\r\n\r\n    // For other errors, return false to allow normal flow\r\n    return false;\r\n  }\r\n}\r\n\r\n// Authentication API functions\r\nexport const authApi = {\r\n  /**\r\n   * Login user with email and password\r\n   * @param data LoginType from zod schema\r\n   */\r\n  login: async (data: LoginType): Promise<LoginResult> => {\r\n    const { email, password } = data;\r\n    try {\r\n      // Prepare login payload\r\n      const payload = {\r\n        email,\r\n        password,\r\n      };\r\n\r\n      const response = await centralizedAuthApi.post<LoginResponse>(\r\n        API_ENDPOINTS.AUTH.LOGIN,\r\n        payload,\r\n      );\r\n\r\n      if (!response.data.access_token) {\r\n        throw new Error(\"Login failed: Unexpected response from server.\");\r\n      }\r\n\r\n      // Set auth cookies after successful login (both server-side and client-side)\r\n      await setAuthCookies(\r\n        response.data.access_token,\r\n        response.data.refresh_token,\r\n        response.data.accessTokenAge || 36000,\r\n        response.data.refreshTokenAge || 86400,\r\n      );\r\n\r\n      // Also set client-side cookies for immediate access\r\n      if (typeof window !== \"undefined\") {\r\n        setClientAuthCookies(\r\n          response.data.access_token,\r\n          response.data.refresh_token,\r\n          response.data.accessTokenAge || 36000,\r\n          response.data.refreshTokenAge || 86400,\r\n        );\r\n        console.log(\"Client-side auth cookies set after login\");\r\n      }\r\n\r\n      let redirectPath = workflowsRoute; // Use workflows route directly\r\n      try {\r\n        // Fetch user details after successful login\r\n        const userDetails = await authApi.getCurrentUser();\r\n\r\n        // Update the user store with user data and access token\r\n        useUserStore.getState().setUser({\r\n          fullName: userDetails.fullName,\r\n          email: userDetails.email,\r\n          accessToken: response.data.access_token,\r\n        });\r\n\r\n        // Keep using workflows route\r\n        redirectPath = workflowsRoute;\r\n      } catch (userError) {\r\n        console.error(\"Failed to fetch user details:\", userError);\r\n        // Keep redirectPath as '/workflows' if fetching user details fails\r\n      }\r\n\r\n      // Return both login data and the determined redirect path\r\n      return {\r\n        loginData: response.data,\r\n        redirectPath,\r\n      };\r\n    } catch (error: any) {\r\n      // Clear user store on login failure\r\n      useUserStore.getState().clearUser();\r\n\r\n      if (error.response?.status === 404) {\r\n        throw new Error(\"User not found.\");\r\n      }\r\n      if (error.response?.status === 412) {\r\n        throw new Error(\"Account inactive. Please check your email for verification.\");\r\n      }\r\n      throw new Error(\r\n        error.response?.data?.detail || error.response?.data?.message || \"Invalid Credentials\",\r\n      );\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Register new user\r\n   * @param data SignupType from zod schema\r\n   */\r\n  signup: async (data: SignupType): Promise<SignupResponse> => {\r\n    try {\r\n      const { email, fullName, password } = data;\r\n      const payload = {\r\n        full_name: fullName,\r\n        email,\r\n        password,\r\n      };\r\n      const response = await centralizedAuthApi.post<SignupResponse>(\r\n        API_ENDPOINTS.AUTH.REGISTER,\r\n        payload,\r\n      );\r\n      return response.data;\r\n    } catch (error: any) {\r\n      if (error.response?.status === 409) {\r\n        throw new Error(error.response?.data?.detail || \"Email already registered.\");\r\n      }\r\n      throw new Error(\r\n        error.response?.data?.detail || error.response?.data?.message || \"Signup failed\",\r\n      );\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Logout user\r\n   */\r\n  logout: async (): Promise<void> => {\r\n    try {\r\n      // Clear server-side auth cookies\r\n      await clearAuthCookies();\r\n\r\n      // Clear client-side auth cookies\r\n      if (typeof window !== \"undefined\") {\r\n        clearClientAuthCookies();\r\n        console.log(\"Client-side auth cookies cleared during logout\");\r\n      }\r\n\r\n      // Clear user store (including accessToken)\r\n      useUserStore.getState().clearUser();\r\n      console.log(\"User store cleared during logout\");\r\n\r\n      // Redirect to login page\r\n      if (typeof window !== \"undefined\") {\r\n        window.location.href = loginRoute;\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"Error during logout:\", error);\r\n\r\n      // Even if there's an error, try to clear everything\r\n      await clearAuthCookies();\r\n      if (typeof window !== \"undefined\") {\r\n        clearClientAuthCookies();\r\n      }\r\n      useUserStore.getState().clearUser();\r\n\r\n      if (typeof window !== \"undefined\") {\r\n        window.location.href = loginRoute;\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Forgot password\r\n   * @param email Email address for password reset\r\n   */\r\n  forgotPassword: async (email: string): Promise<{ message: string }> => {\r\n    try {\r\n      const response = await centralizedAuthApi.post<{ message: string }>(\r\n        API_ENDPOINTS.AUTH.FORGOT_PASSWORD,\r\n        null, // No request body for this POST request\r\n        { params: { email } }, // Pass email as query parameter in the config object\r\n      );\r\n      return response.data; // Return the success message\r\n    } catch (error: any) {\r\n      throw new Error(\r\n        error.response?.data?.detail || // Use detail field if available\r\n          error.response?.data?.message ||\r\n          \"Failed to send password reset email\",\r\n      );\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Reset user's password using OTP token\r\n   * @param token The OTP token from the reset link\r\n   * @param data ResetPasswordType containing newPassword and confirmNewPassword\r\n   */\r\n  resetPassword: async (token: string, data: ResetPasswordType): Promise<{ message: string }> => {\r\n    const { newPassword, confirmNewPassword } = data;\r\n    try {\r\n      // Construct the payload for the API\r\n      const payload = {\r\n        token,\r\n        new_password: newPassword,\r\n        confirm_new_password: confirmNewPassword,\r\n      };\r\n\r\n      // Call the actual API endpoint\r\n      const response = await centralizedAuthApi.post<{ message: string }>(\r\n        API_ENDPOINTS.AUTH.UPDATE_PASSWORD,\r\n        payload,\r\n      );\r\n\r\n      return response.data; // Return the success message from the API\r\n    } catch (error: any) {\r\n      throw new Error(\r\n        // Use error details provided by the API response preferentially\r\n        error.response?.data?.detail || error.response?.data?.message || \"Password reset failed\",\r\n      );\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update user's password\r\n   * @param data Object containing token and password\r\n   */\r\n  updatePassword: async (data: {\r\n    token: string;\r\n    password: string;\r\n  }): Promise<{ message: string }> => {\r\n    try {\r\n      // Construct the payload for the API\r\n      const payload = {\r\n        token: data.token,\r\n        new_password: data.password,\r\n        confirm_new_password: data.password,\r\n      };\r\n\r\n      // Call the actual API endpoint\r\n      const response = await centralizedAuthApi.post<{ message: string }>(\r\n        API_ENDPOINTS.AUTH.UPDATE_PASSWORD,\r\n        payload,\r\n      );\r\n\r\n      return response.data; // Return the success message from the API\r\n    } catch (error: any) {\r\n      throw new Error(\r\n        // Use error details provided by the API response preferentially\r\n        error.response?.data?.detail || error.response?.data?.message || \"Password update failed\",\r\n      );\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Verify email using OTP token\r\n   * @param token The OTP token from the verification link\r\n   */\r\n  verifyEmailOtp: async (token: string): Promise<{ message: string }> => {\r\n    try {\r\n      const payload = { token };\r\n      const response = await centralizedAuthApi.post<{ message: string }>(\r\n        API_ENDPOINTS.AUTH.VERIFY_EMAIL_OTP,\r\n        payload,\r\n      );\r\n      return response.data;\r\n    } catch (error: any) {\r\n      throw new Error(\r\n        error.response?.data?.detail ||\r\n          error.response?.data?.message ||\r\n          \"Email verification failed\",\r\n      );\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get current user information\r\n   */\r\n  getCurrentUser: async () => {\r\n    try {\r\n      console.log(\"Making request to /users/me\");\r\n      // The centralized auth API will automatically handle authorization headers\r\n      const response = await centralizedAuthApi.get(`/users/me`);\r\n      console.log(\"Successfully retrieved user data\");\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error(\"Get current user error:\", error);\r\n\r\n      // Check if this is a 403 Forbidden error\r\n      if (error.response?.status === 403) {\r\n        console.log(\"Authentication error: 403 Forbidden. Token may be invalid or expired.\");\r\n        // Clear user state and cookies to force re-login\r\n        useUserStore.getState().clearUser();\r\n        if (typeof window !== \"undefined\") {\r\n          clearClientAuthCookies();\r\n        }\r\n      }\r\n\r\n      throw new Error(\r\n        error.response?.data?.detail ||\r\n          error.response?.data?.message ||\r\n          \"Failed to fetch user details\",\r\n      );\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Verify current auth session by attempting to fetch user data.\r\n   */\r\n  isLoggedIn: async (): Promise<boolean> => {\r\n    try {\r\n      await authApi.getCurrentUser(); // Attempt to fetch user data\r\n      return true; // If successful, user is considered logged in\r\n    } catch (error) {\r\n      // Any error (including 401/403 from getCurrentUser) means session is not valid\r\n      return false;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Check if user is authenticated based on cookie presence\r\n   * Uses client-side cookie access when in browser environment\r\n   */\r\n  isAuthenticated: async (): Promise<boolean> => {\r\n    // Check if we're in a browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      // Use client-side cookie access\r\n      const isAuth = checkClientAccessToken();\r\n      console.log(\"Client-side isAuthenticated check:\", isAuth);\r\n      return isAuth;\r\n    } else {\r\n      // Use server-side cookie access\r\n      const token = await getAccessToken();\r\n      return !!token;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get the access token from cookies\r\n   * Uses client-side cookie access when in browser environment\r\n   */\r\n  getAccessToken: async (): Promise<string> => {\r\n    // Check if we're in a browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      // Use client-side cookie access\r\n      return getClientAccessToken();\r\n    } else {\r\n      // Use server-side cookie access\r\n      return (await getAccessToken()) || \"\";\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Generate new access token using refresh token\r\n   * @param refreshToken The refresh token to use for generating a new access token\r\n   */\r\n  generateAccessToken: async (refreshToken: string): Promise<TokenResponse> => {\r\n    try {\r\n      const response = await centralizedAuthApi.post<TokenResponse>(\r\n        `/auth/access-token`,\r\n        {},\r\n        {\r\n          params: { refresh_token: refreshToken },\r\n        },\r\n      );\r\n\r\n      // Update the access token in cookies if successful\r\n      if (response.data.success && response.data.access_token) {\r\n        // Calculate token age in seconds from tokenExpireAt\r\n        const expireAt = new Date(response.data.tokenExpireAt).getTime();\r\n        const now = new Date().getTime();\r\n        const accessTokenAge = Math.floor((expireAt - now) / 1000);\r\n\r\n        await setAuthCookies(response.data.access_token, null, accessTokenAge, null);\r\n\r\n        // Also update the access token in the user store\r\n        const currentUser = useUserStore.getState().user;\r\n        if (currentUser) {\r\n          useUserStore.getState().setUser({\r\n            ...currentUser,\r\n            accessToken: response.data.access_token,\r\n          });\r\n        }\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      throw new Error(\r\n        error.response?.data?.detail ||\r\n          error.response?.data?.message ||\r\n          \"Failed to generate new access token\",\r\n      );\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Initiates the Google OAuth login flow by redirecting the browser.\r\n   */\r\n  googleLogin: async (): Promise<void> => {\r\n    try {\r\n      // Construct the full URL to the backend's Google login initiation endpoint.\r\n      let googleLoginUrl = `${API_BASE_URL}/auth/google-login`;\r\n\r\n      // Redirect the user's browser.\r\n      window.location.href = googleLoginUrl;\r\n    } catch (error) {\r\n      console.error(\"Error during Google login:\", error);\r\n      // Fallback to basic Google login without FCM token\r\n      window.location.href = `${API_BASE_URL}/auth/google-login`;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Handles the final steps after Google OAuth callback.\r\n   * Assumes the backend set auth cookies before redirecting back to the frontend.\r\n   */\r\n  finalizeGoogleLogin: async (): Promise<void> => {\r\n    try {\r\n      // Fetch user details using the cookies potentially set by the backend callback\r\n      const userDetails = await authApi.getCurrentUser();\r\n\r\n      // Get the access token from cookies\r\n      const accessToken = await getAccessToken();\r\n\r\n      // Update global user store with user details and access token\r\n      useUserStore.getState().setUser({\r\n        fullName: userDetails.fullName,\r\n        email: userDetails.email,\r\n        accessToken: accessToken,\r\n      });\r\n    } catch (error) {\r\n      useUserStore.getState().clearUser(); // Reset state on error\r\n      console.error(\"Failed to retrieve user details after Google login:\", error);\r\n    }\r\n  },\r\n};\r\n\r\nexport default authApi;\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;AAED;AACA;AACA;AAOA;AAEA;AACA;;;;;;;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,QAAQ,cAAc;QAChD,OAAO,QAAQ,aAAa,cAAc,aAAa;IACzD,EAAE,OAAO,OAAY;QACnB,uEAAuE;QACvE,MAAM,kBAAkB,MAAM,MAAM;QACpC,IAAI,mBAAmB,gBAAgB,MAAM,EAAE;YAC7C,yEAAyE;YACzE,kEAAkE;YAClE,MAAM;QACR;QAEA,sDAAsD;QACtD,OAAO;IACT;AACF;AAGO,MAAM,UAAU;IACrB;;;GAGC,GACD,OAAO,OAAO;QACZ,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;QAC5B,IAAI;YACF,wBAAwB;YACxB,MAAM,UAAU;gBACd;gBACA;YACF;YAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAkB,CAAC,IAAI,CAC5C,yHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,KAAK,EACxB;YAGF,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY,EAAE;gBAC/B,MAAM,IAAI,MAAM;YAClB;YAEA,6EAA6E;YAC7E,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EACjB,SAAS,IAAI,CAAC,YAAY,EAC1B,SAAS,IAAI,CAAC,aAAa,EAC3B,SAAS,IAAI,CAAC,cAAc,IAAI,OAChC,SAAS,IAAI,CAAC,eAAe,IAAI;YAGnC,oDAAoD;YACpD,uCAAmC;;YAQnC;YAEA,IAAI,eAAe,yHAAA,CAAA,iBAAc,EAAE,+BAA+B;YAClE,IAAI;gBACF,4CAA4C;gBAC5C,MAAM,cAAc,MAAM,QAAQ,cAAc;gBAEhD,wDAAwD;gBACxD,2HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,OAAO,CAAC;oBAC9B,UAAU,YAAY,QAAQ;oBAC9B,OAAO,YAAY,KAAK;oBACxB,aAAa,SAAS,IAAI,CAAC,YAAY;gBACzC;gBAEA,6BAA6B;gBAC7B,eAAe,yHAAA,CAAA,iBAAc;YAC/B,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mEAAmE;YACrE;YAEA,0DAA0D;YAC1D,OAAO;gBACL,WAAW,SAAS,IAAI;gBACxB;YACF;QACF,EAAE,OAAO,OAAY;YACnB,oCAAoC;YACpC,2HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS;YAEjC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,IAAI,MACR,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErE;IACF;IAEA;;;GAGC,GACD,QAAQ,OAAO;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;YACtC,MAAM,UAAU;gBACd,WAAW;gBACX;gBACA;YACF;YACA,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAkB,CAAC,IAAI,CAC5C,yHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,QAAQ,EAC3B;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,UAAU;YAClD;YACA,MAAM,IAAI,MACR,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErE;IACF;IAEA;;GAEC,GACD,QAAQ;QACN,IAAI;YACF,iCAAiC;YACjC,MAAM,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD;YAErB,iCAAiC;YACjC,uCAAmC;;YAGnC;YAEA,2CAA2C;YAC3C,2HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS;YACjC,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,uCAAmC;;YAEnC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YAEtC,oDAAoD;YACpD,MAAM,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD;YACrB,uCAAmC;;YAEnC;YACA,2HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS;YAEjC,uCAAmC;;YAEnC;QACF;IACF;IAEA;;;GAGC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAkB,CAAC,IAAI,CAC5C,yHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,eAAe,EAClC,MACA;gBAAE,QAAQ;oBAAE;gBAAM;YAAE;YAEtB,OAAO,SAAS,IAAI,EAAE,6BAA6B;QACrD,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MACR,MAAM,QAAQ,EAAE,MAAM,UAAU,gCAAgC;YAC9D,MAAM,QAAQ,EAAE,MAAM,WACtB;QAEN;IACF;IAEA;;;;GAIC,GACD,eAAe,OAAO,OAAe;QACnC,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,GAAG;QAC5C,IAAI;YACF,oCAAoC;YACpC,MAAM,UAAU;gBACd;gBACA,cAAc;gBACd,sBAAsB;YACxB;YAEA,+BAA+B;YAC/B,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAkB,CAAC,IAAI,CAC5C,yHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,eAAe,EAClC;YAGF,OAAO,SAAS,IAAI,EAAE,0CAA0C;QAClE,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MACR,gEAAgE;YAChE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErE;IACF;IAEA;;;GAGC,GACD,gBAAgB,OAAO;QAIrB,IAAI;YACF,oCAAoC;YACpC,MAAM,UAAU;gBACd,OAAO,KAAK,KAAK;gBACjB,cAAc,KAAK,QAAQ;gBAC3B,sBAAsB,KAAK,QAAQ;YACrC;YAEA,+BAA+B;YAC/B,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAkB,CAAC,IAAI,CAC5C,yHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,eAAe,EAClC;YAGF,OAAO,SAAS,IAAI,EAAE,0CAA0C;QAClE,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MACR,gEAAgE;YAChE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErE;IACF;IAEA;;;GAGC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,UAAU;gBAAE;YAAM;YACxB,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAkB,CAAC,IAAI,CAC5C,yHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,gBAAgB,EACnC;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MACR,MAAM,QAAQ,EAAE,MAAM,UACpB,MAAM,QAAQ,EAAE,MAAM,WACtB;QAEN;IACF;IAEA;;GAEC,GACD,gBAAgB;QACd,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,2EAA2E;YAC3E,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAkB,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,yCAAyC;YACzC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,QAAQ,GAAG,CAAC;gBACZ,iDAAiD;gBACjD,2HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS;gBACjC,uCAAmC;;gBAEnC;YACF;YAEA,MAAM,IAAI,MACR,MAAM,QAAQ,EAAE,MAAM,UACpB,MAAM,QAAQ,EAAE,MAAM,WACtB;QAEN;IACF;IAEA;;GAEC,GACD,YAAY;QACV,IAAI;YACF,MAAM,QAAQ,cAAc,IAAI,6BAA6B;YAC7D,OAAO,MAAM,8CAA8C;QAC7D,EAAE,OAAO,OAAO;YACd,+EAA+E;YAC/E,OAAO;QACT;IACF;IAEA;;;GAGC,GACD,iBAAiB;QACf,0CAA0C;QAC1C,uCAAmC;;QAKnC,OAAO;YACL,gCAAgC;YAChC,MAAM,QAAQ,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;YACjC,OAAO,CAAC,CAAC;QACX;IACF;IAEA;;;GAGC,GACD,gBAAgB;QACd,0CAA0C;QAC1C,uCAAmC;;QAGnC,OAAO;YACL,gCAAgC;YAChC,OAAO,AAAC,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,OAAQ;QACrC;IACF;IAEA;;;GAGC,GACD,qBAAqB,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAkB,CAAC,IAAI,CAC5C,CAAC,kBAAkB,CAAC,EACpB,CAAC,GACD;gBACE,QAAQ;oBAAE,eAAe;gBAAa;YACxC;YAGF,mDAAmD;YACnD,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,YAAY,EAAE;gBACvD,oDAAoD;gBACpD,MAAM,WAAW,IAAI,KAAK,SAAS,IAAI,CAAC,aAAa,EAAE,OAAO;gBAC9D,MAAM,MAAM,IAAI,OAAO,OAAO;gBAC9B,MAAM,iBAAiB,KAAK,KAAK,CAAC,CAAC,WAAW,GAAG,IAAI;gBAErD,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,IAAI,CAAC,YAAY,EAAE,MAAM,gBAAgB;gBAEvE,iDAAiD;gBACjD,MAAM,cAAc,2HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;gBAChD,IAAI,aAAa;oBACf,2HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,OAAO,CAAC;wBAC9B,GAAG,WAAW;wBACd,aAAa,SAAS,IAAI,CAAC,YAAY;oBACzC;gBACF;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MACR,MAAM,QAAQ,EAAE,MAAM,UACpB,MAAM,QAAQ,EAAE,MAAM,WACtB;QAEN;IACF;IAEA;;GAEC,GACD,aAAa;QACX,IAAI;YACF,4EAA4E;YAC5E,IAAI,iBAAiB,GAAG,yHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC;YAExD,+BAA+B;YAC/B,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,mDAAmD;YACnD,OAAO,QAAQ,CAAC,IAAI,GAAG,GAAG,yHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC;QAC5D;IACF;IAEA;;;GAGC,GACD,qBAAqB;QACnB,IAAI;YACF,+EAA+E;YAC/E,MAAM,cAAc,MAAM,QAAQ,cAAc;YAEhD,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;YAEvC,8DAA8D;YAC9D,2HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,OAAO,CAAC;gBAC9B,UAAU,YAAY,QAAQ;gBAC9B,OAAO,YAAY,KAAK;gBACxB,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,2HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS,IAAI,uBAAuB;YAC5D,QAAQ,KAAK,CAAC,uDAAuD;QACvE;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/app/api/auth/route.ts"], "sourcesContent": ["/**\r\n * Authentication API Route Handler\r\n *\r\n * This file provides API routes for authentication operations, acting as a server-side\r\n * proxy between the client and the external authentication API.\r\n */\r\n\r\nimport { NextRequest, NextResponse } from 'next/server';\r\nimport { cookies } from 'next/headers';\r\nimport { authApi } from '@/lib/authApi';\r\nimport { setAuthCookies, clearAuthCookies } from '@/lib/cookies';\r\n\r\n/**\r\n * POST handler for authentication operations\r\n * Supports:\r\n * - /api/auth/login - Login with email and password\r\n * - /api/auth/signup - Register a new user\r\n * - /api/auth/logout - Logout the current user\r\n * - /api/auth/refresh - Refresh the access token\r\n */\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const { action, ...data } = await request.json();\r\n\r\n    switch (action) {\r\n      case 'login': {\r\n        const result = await authApi.login(data);\r\n\r\n        // Set cookies on the server side\r\n        if (result.loginData.access_token) {\r\n          await setAuthCookies(\r\n            result.loginData.access_token,\r\n            result.loginData.refresh_token,\r\n            result.loginData.accessTokenAge || 3600,\r\n            result.loginData.refreshTokenAge || 86400\r\n          );\r\n        }\r\n\r\n        return NextResponse.json({\r\n          success: true,\r\n          redirectPath: result.redirectPath || '/workflows'\r\n        });\r\n      }\r\n\r\n      case 'signup': {\r\n        const result = await authApi.signup(data);\r\n        return NextResponse.json({\r\n          success: true,\r\n          message: result.message || 'Registration successful'\r\n        });\r\n      }\r\n\r\n      case 'logout': {\r\n        await clearAuthCookies();\r\n        return NextResponse.json({\r\n          success: true,\r\n          message: 'Logged out successfully'\r\n        });\r\n      }\r\n\r\n      case 'refresh': {\r\n        const cookieStore = await cookies();\r\n        const refreshToken = cookieStore.get('refreshToken')?.value;\r\n\r\n        if (!refreshToken) {\r\n          return NextResponse.json(\r\n            { success: false, message: 'No refresh token found' },\r\n            { status: 401 }\r\n          );\r\n        }\r\n\r\n        const result = await authApi.generateAccessToken(refreshToken);\r\n\r\n        if (result.success && result.access_token) {\r\n          // Set the new access token in cookies\r\n          await setAuthCookies(\r\n            result.access_token,\r\n            refreshToken,\r\n            3600, // Default token age\r\n            86400 // Default refresh token age\r\n          );\r\n\r\n          return NextResponse.json({\r\n            success: true,\r\n            message: 'Token refreshed successfully'\r\n          });\r\n        } else {\r\n          return NextResponse.json(\r\n            { success: false, message: 'Failed to refresh token' },\r\n            { status: 401 }\r\n          );\r\n        }\r\n      }\r\n\r\n      default:\r\n        return NextResponse.json(\r\n          { success: false, message: 'Invalid action' },\r\n          { status: 400 }\r\n        );\r\n    }\r\n  } catch (error: any) {\r\n    console.error('Error in auth API route:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: error.message || 'Authentication failed',\r\n        details: error.response?.data || null\r\n      },\r\n      { status: error.response?.status || 500 }\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * GET handler for retrieving the current user\r\n */\r\nexport async function GET() {\r\n  try {\r\n    const user = await authApi.getCurrentUser();\r\n    return NextResponse.json({ success: true, user });\r\n  } catch (error: any) {\r\n    console.error('Error fetching current user:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: error.message || 'Failed to fetch user',\r\n        details: error.response?.data || null\r\n      },\r\n      { status: error.response?.status || 500 }\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * OPTIONS handler for CORS preflight requests\r\n */\r\nexport async function OPTIONS() {\r\n  return new NextResponse(null, {\r\n    status: 200,\r\n    headers: {\r\n      'Access-Control-Allow-Origin': '*',\r\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\r\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAED;AACA;AACA;AACA;;;;;AAUO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,MAAM,QAAQ,IAAI;QAE9C,OAAQ;YACN,KAAK;gBAAS;oBACZ,MAAM,SAAS,MAAM,uHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;oBAEnC,iCAAiC;oBACjC,IAAI,OAAO,SAAS,CAAC,YAAY,EAAE;wBACjC,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EACjB,OAAO,SAAS,CAAC,YAAY,EAC7B,OAAO,SAAS,CAAC,aAAa,EAC9B,OAAO,SAAS,CAAC,cAAc,IAAI,MACnC,OAAO,SAAS,CAAC,eAAe,IAAI;oBAExC;oBAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,cAAc,OAAO,YAAY,IAAI;oBACvC;gBACF;YAEA,KAAK;gBAAU;oBACb,MAAM,SAAS,MAAM,uHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;oBACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,SAAS,OAAO,OAAO,IAAI;oBAC7B;gBACF;YAEA,KAAK;gBAAU;oBACb,MAAM,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD;oBACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,SAAS;oBACX;gBACF;YAEA,KAAK;gBAAW;oBACd,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;oBAChC,MAAM,eAAe,YAAY,GAAG,CAAC,iBAAiB;oBAEtD,IAAI,CAAC,cAAc;wBACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;4BAAE,SAAS;4BAAO,SAAS;wBAAyB,GACpD;4BAAE,QAAQ;wBAAI;oBAElB;oBAEA,MAAM,SAAS,MAAM,uHAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;oBAEjD,IAAI,OAAO,OAAO,IAAI,OAAO,YAAY,EAAE;wBACzC,sCAAsC;wBACtC,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EACjB,OAAO,YAAY,EACnB,cACA,MACA,MAAM,4BAA4B;;wBAGpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;4BACvB,SAAS;4BACT,SAAS;wBACX;oBACF,OAAO;wBACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;4BAAE,SAAS;4BAAO,SAAS;wBAA0B,GACrD;4BAAE,QAAQ;wBAAI;oBAElB;gBACF;YAEA;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS;gBAAiB,GAC5C;oBAAE,QAAQ;gBAAI;QAEpB;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,MAAM,OAAO,IAAI;YACxB,SAAS,MAAM,QAAQ,EAAE,QAAQ;QACnC,GACA;YAAE,QAAQ,MAAM,QAAQ,EAAE,UAAU;QAAI;IAE5C;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAO,CAAC,cAAc;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM;QAAK;IACjD,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,MAAM,OAAO,IAAI;YACxB,SAAS,MAAM,QAAQ,EAAE,QAAQ;QACnC,GACA;YAAE,QAAQ,MAAM,QAAQ,EAAE,UAAU;QAAI;IAE5C;AACF;AAKO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}