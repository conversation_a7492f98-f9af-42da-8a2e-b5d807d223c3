module.exports = {

"[project]/.next-internal/server/app/api/auth/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/utils/authCookies.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/utils/authCookies.ts
__turbopack_context__.s({
    "ACCESS_TOKEN_NAME": (()=>ACCESS_TOKEN_NAME),
    "REFRESH_TOKEN_NAME": (()=>REFRESH_TOKEN_NAME),
    "clearAuthCookies": (()=>clearAuthCookies),
    "getAccessToken": (()=>getAccessToken),
    "getRefreshToken": (()=>getRefreshToken),
    "setAccessToken": (()=>setAccessToken),
    "setRefreshToken": (()=>setRefreshToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cookies-next/lib/index.js [app-route] (ecmascript)");
;
const ACCESS_TOKEN_NAME = "access_token";
const REFRESH_TOKEN_NAME = "refresh_token";
const getAccessToken = async ()=>{
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCookie"])(ACCESS_TOKEN_NAME);
    return token ? String(token) : null;
};
const getRefreshToken = async ()=>{
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCookie"])(REFRESH_TOKEN_NAME);
    return token ? String(token) : null;
};
const setAccessToken = (token, expiresIn = 3600)=>{
    const expirationDate = new Date();
    expirationDate.setSeconds(expirationDate.getSeconds() + expiresIn);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setCookie"])(ACCESS_TOKEN_NAME, token, {
        expires: expirationDate,
        path: "/",
        secure: ("TURBOPACK compile-time value", "development") === "production",
        sameSite: "strict"
    });
};
const setRefreshToken = (token, expiresIn = 30 * 24 * 60 * 60)=>{
    const expirationDate = new Date();
    expirationDate.setSeconds(expirationDate.getSeconds() + expiresIn);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setCookie"])(REFRESH_TOKEN_NAME, token, {
        expires: expirationDate,
        path: "/",
        secure: ("TURBOPACK compile-time value", "development") === "production",
        sameSite: "strict"
    });
};
const clearAuthCookies = async ()=>{
    // Clear access token with default path
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["deleteCookie"])(ACCESS_TOKEN_NAME, {
        path: "/"
    });
    // Clear refresh token with both possible paths to ensure it's removed
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["deleteCookie"])(REFRESH_TOKEN_NAME, {
        path: "/"
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cookies$2d$next$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["deleteCookie"])(REFRESH_TOKEN_NAME, {
        path: "/api/auth/refresh"
    });
    console.log("Auth cookies cleared with specific paths");
};
}}),
"[project]/src/lib/clientCookies.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Client-side cookie utilities
 * These functions can be used in client components to get/set cookies in the browser
 */ __turbopack_context__.s({
    "checkClientAccessToken": (()=>checkClientAccessToken),
    "clearClientAuthCookies": (()=>clearClientAuthCookies),
    "getClientAccessToken": (()=>getClientAccessToken),
    "getClientRefreshToken": (()=>getClientRefreshToken),
    "setClientAuthCookies": (()=>setClientAuthCookies)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-route] (ecmascript)");
;
const getClientAccessToken = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get("accessToken") || "";
};
const getClientRefreshToken = ()=>{
    // The refresh token is now HTTP-only and not accessible from client-side JavaScript
    // This function is kept for backward compatibility but will always return null
    return null;
};
const checkClientAccessToken = ()=>{
    const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get("accessToken");
    console.log("Client-side access token check:", !!token);
    return !!token;
};
const setClientAuthCookies = (accessToken, refreshToken, accessTokenAge, refreshTokenAge)=>{
    // Set access token cookie
    // Note: The refresh token is handled by the server-side HTTP-only cookie
    // and should not be set on the client side
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].set("accessToken", accessToken, {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost"),
        secure: true,
        sameSite: "lax",
        expires: accessTokenAge / (60 * 60 * 24)
    });
// We no longer set the refresh token on the client side
// as it's now handled by an HTTP-only cookie set by the server
// This improves security by preventing client-side JavaScript from accessing the refresh token
};
const clearClientAuthCookies = ()=>{
    // Clear access token from client-side cookie
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].remove("accessToken", {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost")
    });
    // Attempt to clear refresh token with both possible paths
    // Even though it's HTTP-only, we try to clear it from client-side as a fallback
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].remove("refreshToken", {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost")
    });
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].remove("refreshToken", {
        path: "/api/auth/refresh",
        domain: ("TURBOPACK compile-time value", "localhost")
    });
    console.log("Client-side cookie clearing attempted for both tokens");
};
}}),
"[project]/src/shared/routes.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Shared route definitions for the application
 * Matches the implementation in ruh-app-fe for consistency.
 */ // Authentication routes
__turbopack_context__.s({
    "aboutRoute": (()=>aboutRoute),
    "authRoute": (()=>authRoute),
    "contactRoute": (()=>contactRoute),
    "credentialsRoute": (()=>credentialsRoute),
    "homeRoute": (()=>homeRoute),
    "loginRoute": (()=>loginRoute),
    "protectedRoutes": (()=>protectedRoutes),
    "publicRoutes": (()=>publicRoutes),
    "settingsRoute": (()=>settingsRoute),
    "signupRoute": (()=>signupRoute),
    "updatePasswordRoute": (()=>updatePasswordRoute),
    "verifyEmailRoute": (()=>verifyEmailRoute),
    "workflowsRoute": (()=>workflowsRoute)
});
const loginRoute = "/login";
const signupRoute = "/signup";
const verifyEmailRoute = "/verify-email";
const updatePasswordRoute = "/reset-password";
const authRoute = `${("TURBOPACK compile-time value", "http://localhost:3001/")}?redirect_url=${("TURBOPACK compile-time value", "http://localhost:3000/")}`;
const homeRoute = "/workflows"; // Updated to point to workflows instead of /home
const workflowsRoute = "/workflows";
const settingsRoute = "/settings";
const credentialsRoute = "/credentials";
const aboutRoute = "/about";
const contactRoute = "/contact";
const publicRoutes = [
    loginRoute,
    signupRoute,
    verifyEmailRoute,
    updatePasswordRoute,
    aboutRoute,
    contactRoute
];
const protectedRoutes = [
    workflowsRoute,
    workflowsRoute,
    settingsRoute,
    credentialsRoute
];
}}),
"[project]/src/hooks/use-user.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/hooks/use-user.ts
__turbopack_context__.s({
    "useUserStore": (()=>useUserStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-route] (ecmascript)");
;
;
const useUserStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        user: null,
        setUser: (user)=>set({
                user: {
                    ...user,
                    isAuthenticated: true
                }
            }),
        clearUser: ()=>set({
                user: null
            }),
        isAuthenticated: ()=>!!get().user?.isAuthenticated
    }), {
    name: "user-storage"
}));
}}),
"[project]/src/utils/axios.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authApi": (()=>authApi),
    "createAxiosInstance": (()=>createAxiosInstance),
    "default": (()=>__TURBOPACK__default__export__),
    "externalApi": (()=>externalApi),
    "logout": (()=>logout),
    "workflowApi": (()=>workflowApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/authCookies.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/clientCookies.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/routes.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-user.ts [app-route] (ecmascript)");
;
;
;
;
;
// Helper function to clear user store
const clearUserStore = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$user$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
};
// Helper function to get appropriate token based on environment
const getAppropriateToken = async (enableClientSide = false)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side or fallback
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAccessToken"])();
    }
};
// Create request interceptor
const createRequestInterceptor = (config = {})=>{
    return async (requestConfig)=>{
        // Ensure headers object exists
        if (!requestConfig.headers) {
            requestConfig.headers = {};
        }
        const token = await getAppropriateToken(config.enableClientSideToken);
        if (token) {
            requestConfig.headers.Authorization = `Bearer ${token}`;
            console.log(`[DEBUG] Added Authorization header with token (length: ${token.length})`);
        } else {
            console.log(`[DEBUG] No token available for request to ${requestConfig.url}`);
        }
        // Add common headers
        requestConfig.headers["ngrok-skip-browser-warning"] = "true";
        // Add custom headers if provided
        if (config.customHeaders) {
            Object.assign(requestConfig.headers, config.customHeaders);
        }
        return requestConfig;
    };
};
// Create response interceptor
const createResponseInterceptor = (instance, config = {})=>{
    return async (error)=>{
        const originalRequest = error.config;
        // Only handle token refresh if enabled
        if (!config.enableTokenRefresh) {
            return Promise.reject(error);
        }
        // Check if the error is due to an expired token (401 Unauthorized or 403 Forbidden)
        if ((error.response?.status === 401 || error.response?.status === 403) && !originalRequest._retry) {
            // Mark this request as retried to prevent infinite loops
            originalRequest._retry = true;
            try {
                // Call the refresh token endpoint using a fresh axios instance to avoid infinite loops
                // This endpoint will use the HTTP-only refresh token cookie automatically
                const refreshInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].create();
                const response = await refreshInstance.post("/api/auth/refresh");
                if (response.data.success && response.data.accessToken) {
                    // Update the authorization header with the new token
                    originalRequest.headers = {
                        ...originalRequest.headers,
                        Authorization: `Bearer ${response.data.accessToken}`
                    };
                    // Retry the original request with the new token
                    return instance(originalRequest);
                } else {
                    // Token refresh failed
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
                    clearUserStore();
                    if ("TURBOPACK compile-time falsy", 0) {
                        "TURBOPACK unreachable";
                    }
                    return Promise.reject(new Error("Token refresh failed"));
                }
            } catch (refreshError) {
                // Clear cookies and redirect to login on refresh error
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
                clearUserStore();
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                }
                return Promise.reject(refreshError);
            }
        }
        // For other errors, just reject the promise
        return Promise.reject(error);
    };
};
const createAxiosInstance = (config = {})=>{
    const instance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].create({
        baseURL: config.baseURL || ("TURBOPACK compile-time value", "https://gateway-api-version-integration.rapidinnovation.dev/api/v1"),
        withCredentials: config.withCredentials ?? false
    });
    // Add request interceptor
    instance.interceptors.request.use(createRequestInterceptor(config), (error)=>{
        return Promise.reject(new Error(`Request interceptor error: ${error.message || "Unknown error"}`));
    });
    // Add response interceptor
    instance.interceptors.response.use((response)=>response, createResponseInterceptor(instance, config));
    return instance;
};
// Main API instance with full token refresh capabilities
const api = createAxiosInstance({
    enableTokenRefresh: true,
    enableClientSideToken: true
});
const workflowApi = createAxiosInstance({
    enableTokenRefresh: false,
    enableClientSideToken: true
});
const authApi = createAxiosInstance({
    enableTokenRefresh: true,
    enableClientSideToken: true,
    withCredentials: true
});
const externalApi = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].create();
const logout = async ()=>{
    try {
        // Clear all auth cookies (both access and refresh tokens)
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
        // Clear user store
        clearUserStore();
        // Redirect to login page
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    } catch (error) {
        console.error("Error during logout:", error);
        // Even if there's an error, try to clear everything
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$authCookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
        clearUserStore();
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
};
const __TURBOPACK__default__export__ = api;
}}),
"[project]/src/lib/cookies.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ {"7f06e5dfd84ab0c07ed31b04d4f37ae17ace602a2c":"setRefreshingTokenCookie","7f6afb28085f3f5ab4bce42ecd8376b99d1e52fa86":"getRefreshToken","7f7585f7cb94f28ad986a1c31916a1a410528e6cd1":"getAccessToken","7f7eeb76341200e95f0cfaf6a1775b37ae31bcda34":"clearAuthCookies","7fabf9d42b3dd78768353f5b837f3883332691548e":"setAuthCookies","7fc5568e0d82b85c58be597d55e74cee163135f728":"checkAccessToken","7fff563ba7c3f776eb4d4439783e3dcdde31d52169":"clearRefreshingTokenCookie"} */ __turbopack_context__.s({
    "checkAccessToken": (()=>checkAccessToken),
    "clearAuthCookies": (()=>clearAuthCookies),
    "clearRefreshingTokenCookie": (()=>clearRefreshingTokenCookie),
    "getAccessToken": (()=>getAccessToken),
    "getRefreshToken": (()=>getRefreshToken),
    "setAuthCookies": (()=>setAuthCookies),
    "setRefreshingTokenCookie": (()=>setRefreshingTokenCookie)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-route] (ecmascript)");
;
;
;
const /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ getAccessToken = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    const accessToken = cookieStore.get("accessToken");
    if (accessToken) {
        return accessToken.value;
    } else {
        return "";
    }
};
const /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ getRefreshToken = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    const refreshToken = cookieStore.get("refreshToken");
    return refreshToken?.value || null;
};
const /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ checkAccessToken = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    const tokenCookie = cookieStore.get("accessToken");
    return Boolean(tokenCookie?.value);
};
const /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ setAuthCookies = async (accessToken, refreshToken, accessTokenAge, refreshTokenAge)=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    // Set access token as a non-HttpOnly cookie (accessible to JavaScript)
    // This is for client-side access to the token for API calls
    cookieStore.set("accessToken", accessToken, {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost"),
        httpOnly: false,
        sameSite: "lax",
        secure: true,
        maxAge: accessTokenAge
    });
    // Set a secure HTTP-only cookie for the refresh token
    // This provides better security as it's not accessible to JavaScript
    if (refreshToken && refreshTokenAge) {
        cookieStore.set("refreshToken", refreshToken, {
            path: "/api/auth/refresh",
            domain: ("TURBOPACK compile-time value", "localhost"),
            httpOnly: true,
            sameSite: "lax",
            secure: true,
            maxAge: refreshTokenAge
        });
    }
};
const /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ clearAuthCookies = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    // Clear access token cookie
    cookieStore.set("accessToken", "", {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost"),
        httpOnly: false,
        secure: true,
        sameSite: "lax",
        maxAge: 0,
        expires: new Date(0)
    });
    // Clear refresh token cookie with the specific path used when setting it
    cookieStore.set("refreshToken", "", {
        path: "/api/auth/refresh",
        domain: ("TURBOPACK compile-time value", "localhost"),
        httpOnly: true,
        secure: true,
        sameSite: "lax",
        maxAge: 0,
        expires: new Date(0)
    });
    // Also clear refresh token with root path as a fallback
    cookieStore.set("refreshToken", "", {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost"),
        httpOnly: true,
        secure: true,
        sameSite: "lax",
        maxAge: 0,
        expires: new Date(0)
    });
    console.log("Server-side auth cookies cleared with multiple paths");
};
const /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ setRefreshingTokenCookie = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.set("refreshingToken", "true", {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost"),
        httpOnly: false,
        sameSite: "none",
        maxAge: 60
    });
};
const /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ clearRefreshingTokenCookie = async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.set("refreshingToken", "", {
        path: "/",
        domain: ("TURBOPACK compile-time value", "localhost"),
        maxAge: 0
    });
};
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getAccessToken,
    getRefreshToken,
    checkAccessToken,
    setAuthCookies,
    clearAuthCookies,
    setRefreshingTokenCookie,
    clearRefreshingTokenCookie
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(getAccessToken, "7f7585f7cb94f28ad986a1c31916a1a410528e6cd1", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(getRefreshToken, "7f6afb28085f3f5ab4bce42ecd8376b99d1e52fa86", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(checkAccessToken, "7fc5568e0d82b85c58be597d55e74cee163135f728", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(setAuthCookies, "7fabf9d42b3dd78768353f5b837f3883332691548e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(clearAuthCookies, "7f7eeb76341200e95f0cfaf6a1775b37ae31bcda34", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(setRefreshingTokenCookie, "7f06e5dfd84ab0c07ed31b04d4f37ae17ace602a2c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(clearRefreshingTokenCookie, "7fff563ba7c3f776eb4d4439783e3dcdde31d52169", null);
}}),
"[project]/src/store/userStore.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * User store for authentication state management
 * Matches the implementation in ruh-app-fe for consistency.
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "useUserStore": (()=>useUserStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-route] (ecmascript)");
;
;
const useUserStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        // Actions
        setUser: (userData)=>set((state)=>({
                    // Merge existing user data with new data if user exists
                    user: state.user ? {
                        ...state.user,
                        ...userData
                    } : userData,
                    // Update isAuthenticated based on presence of accessToken
                    isAuthenticated: userData?.accessToken ? true : false
                })),
        clearUser: ()=>set({
                user: null,
                isAuthenticated: false
            }),
        // Logout action that calls the authApi logout method
        logout: async ()=>{
            try {
                // Import dynamically to avoid circular dependencies
                const { authApi } = await __turbopack_context__.r("[project]/src/lib/authApi.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                await authApi.logout();
            } catch (error) {
                console.error("Error during logout:", error);
                // Clear user state even if logout API fails
                get().clearUser();
            }
        }
    }), {
    name: "user-storage"
}));
const __TURBOPACK__default__export__ = useUserStore;
}}),
"[project]/src/lib/apiConfig.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API Configuration
 *
 * This file centralizes all API URL configurations to ensure consistency
 * across the application.
 *
 * Note: The API_BASE_URL already includes '/api/v1' from the environment variable
 * (e.g., https://app-dev.rapidinnovation.dev/api/v1)
 */ // Base API URL from environment variable (already includes /api/v1)
__turbopack_context__.s({
    "API_BASE_URL": (()=>API_BASE_URL),
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "AUTH_API_URL": (()=>AUTH_API_URL),
    "MCPS_API_URL": (()=>MCPS_API_URL),
    "WORKFLOWS_API_URL": (()=>WORKFLOWS_API_URL),
    "WORKFLOW_EXECUTION_URL": (()=>WORKFLOW_EXECUTION_URL),
    "default": (()=>__TURBOPACK__default__export__)
});
const API_BASE_URL = ("TURBOPACK compile-time value", "https://gateway-api-version-integration.rapidinnovation.dev/api/v1");
const AUTH_API_URL = `${API_BASE_URL}/auth`;
const WORKFLOWS_API_URL = `${API_BASE_URL}/workflows`;
const MCPS_API_URL = `${API_BASE_URL}/mcps`;
const WORKFLOW_EXECUTION_URL = ("TURBOPACK compile-time value", "https://ruh-test-api.rapidinnovation.dev/api/v1");
const API_ENDPOINTS = {
    // Auth endpoints
    AUTH: {
        LOGIN: `${AUTH_API_URL}/login`,
        REGISTER: `${AUTH_API_URL}/register`,
        LOGOUT: `${AUTH_API_URL}/logout`,
        REFRESH: `${AUTH_API_URL}/refresh`,
        FORGOT_PASSWORD: `${AUTH_API_URL}/forgot-password`,
        RESET_PASSWORD: `${AUTH_API_URL}/reset-password`,
        VERIFY_EMAIL: `${AUTH_API_URL}/verify-email`,
        VERIFY_EMAIL_OTP: `${AUTH_API_URL}/verify-email-otp`,
        UPDATE_PASSWORD: `${AUTH_API_URL}/update-password`
    },
    // Workflow endpoints
    WORKFLOWS: {
        LIST: `${WORKFLOWS_API_URL}`,
        CREATE: `${WORKFLOWS_API_URL}`,
        GET: (id)=>`${WORKFLOWS_API_URL}/${id}`,
        UPDATE: (id)=>`${WORKFLOWS_API_URL}/${id}`,
        DELETE: (id)=>`${WORKFLOWS_API_URL}/${id}`,
        EXECUTE: (id)=>`${WORKFLOWS_API_URL}/${id}/execute`
    },
    // MCP endpoints
    MCPS: {
        LIST: `${MCPS_API_URL}`,
        GET: (id)=>`${MCPS_API_URL}/${id}`
    },
    // Workflow Execution endpoints (separate service)
    WORKFLOW_EXECUTION: {
        EXECUTE: `${WORKFLOW_EXECUTION_URL}/workflow-execute/execute`,
        APPROVE: `${WORKFLOW_EXECUTION_URL}/workflow-execute/approve`,
        STREAM: `${WORKFLOW_EXECUTION_URL}/workflow-execute/stream`
    }
};
const __TURBOPACK__default__export__ = {
    API_BASE_URL,
    AUTH_API_URL,
    WORKFLOWS_API_URL,
    MCPS_API_URL,
    WORKFLOW_EXECUTION_URL,
    API_ENDPOINTS
};
}}),
"[project]/src/lib/authApi.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Authentication API client for workflow-builder-poc
 * This module provides functions for authentication operations.
 * Matches the implementation in ruh-app-fe for consistency.
 */ __turbopack_context__.s({
    "authApi": (()=>authApi),
    "default": (()=>__TURBOPACK__default__export__),
    "hasCompletedOnboarding": (()=>hasCompletedOnboarding)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/axios.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cookies.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$clientCookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/clientCookies.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/userStore.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiConfig.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/routes.ts [app-route] (ecmascript)");
;
;
;
;
;
;
async function hasCompletedOnboarding() {
    try {
        const userDetails = await authApi.getCurrentUser();
        return Boolean(userDetails?.department && userDetails?.jobRole);
    } catch (error) {
        // Check if this request has already been through the axios interceptor
        const originalRequest = error.config;
        if (originalRequest && originalRequest._retry) {
            // This request has already been through the interceptor and still failed
            // This means token refresh failed, so we should redirect to login
            throw error;
        }
        // For other errors, return false to allow normal flow
        return false;
    }
}
const authApi = {
    /**
   * Login user with email and password
   * @param data LoginType from zod schema
   */ login: async (data)=>{
        const { email, password } = data;
        try {
            // Prepare login payload
            const payload = {
                email,
                password
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.LOGIN, payload);
            if (!response.data.access_token) {
                throw new Error("Login failed: Unexpected response from server.");
            }
            // Set auth cookies after successful login (both server-side and client-side)
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setAuthCookies"])(response.data.access_token, response.data.refresh_token, response.data.accessTokenAge || 36000, response.data.refreshTokenAge || 86400);
            // Also set client-side cookies for immediate access
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            let redirectPath = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["workflowsRoute"]; // Use workflows route directly
            try {
                // Fetch user details after successful login
                const userDetails = await authApi.getCurrentUser();
                // Update the user store with user data and access token
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().setUser({
                    fullName: userDetails.fullName,
                    email: userDetails.email,
                    accessToken: response.data.access_token
                });
                // Keep using workflows route
                redirectPath = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["workflowsRoute"];
            } catch (userError) {
                console.error("Failed to fetch user details:", userError);
            // Keep redirectPath as '/workflows' if fetching user details fails
            }
            // Return both login data and the determined redirect path
            return {
                loginData: response.data,
                redirectPath
            };
        } catch (error) {
            // Clear user store on login failure
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
            if (error.response?.status === 404) {
                throw new Error("User not found.");
            }
            if (error.response?.status === 412) {
                throw new Error("Account inactive. Please check your email for verification.");
            }
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Invalid Credentials");
        }
    },
    /**
   * Register new user
   * @param data SignupType from zod schema
   */ signup: async (data)=>{
        try {
            const { email, fullName, password } = data;
            const payload = {
                full_name: fullName,
                email,
                password
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.REGISTER, payload);
            return response.data;
        } catch (error) {
            if (error.response?.status === 409) {
                throw new Error(error.response?.data?.detail || "Email already registered.");
            }
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Signup failed");
        }
    },
    /**
   * Logout user
   */ logout: async ()=>{
        try {
            // Clear server-side auth cookies
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
            // Clear client-side auth cookies
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            // Clear user store (including accessToken)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
            console.log("User store cleared during logout");
            // Redirect to login page
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
        } catch (error) {
            console.error("Error during logout:", error);
            // Even if there's an error, try to clear everything
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
        }
    },
    /**
   * Forgot password
   * @param email Email address for password reset
   */ forgotPassword: async (email)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.FORGOT_PASSWORD, null, {
                params: {
                    email
                }
            });
            return response.data; // Return the success message
        } catch (error) {
            throw new Error(error.response?.data?.detail || // Use detail field if available
            error.response?.data?.message || "Failed to send password reset email");
        }
    },
    /**
   * Reset user's password using OTP token
   * @param token The OTP token from the reset link
   * @param data ResetPasswordType containing newPassword and confirmNewPassword
   */ resetPassword: async (token, data)=>{
        const { newPassword, confirmNewPassword } = data;
        try {
            // Construct the payload for the API
            const payload = {
                token,
                new_password: newPassword,
                confirm_new_password: confirmNewPassword
            };
            // Call the actual API endpoint
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.UPDATE_PASSWORD, payload);
            return response.data; // Return the success message from the API
        } catch (error) {
            throw new Error(// Use error details provided by the API response preferentially
            error.response?.data?.detail || error.response?.data?.message || "Password reset failed");
        }
    },
    /**
   * Update user's password
   * @param data Object containing token and password
   */ updatePassword: async (data)=>{
        try {
            // Construct the payload for the API
            const payload = {
                token: data.token,
                new_password: data.password,
                confirm_new_password: data.password
            };
            // Call the actual API endpoint
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.UPDATE_PASSWORD, payload);
            return response.data; // Return the success message from the API
        } catch (error) {
            throw new Error(// Use error details provided by the API response preferentially
            error.response?.data?.detail || error.response?.data?.message || "Password update failed");
        }
    },
    /**
   * Verify email using OTP token
   * @param token The OTP token from the verification link
   */ verifyEmailOtp: async (token)=>{
        try {
            const payload = {
                token
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.VERIFY_EMAIL_OTP, payload);
            return response.data;
        } catch (error) {
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Email verification failed");
        }
    },
    /**
   * Get current user information
   */ getCurrentUser: async ()=>{
        try {
            console.log("Making request to /users/me");
            // The centralized auth API will automatically handle authorization headers
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].get(`/users/me`);
            console.log("Successfully retrieved user data");
            return response.data;
        } catch (error) {
            console.error("Get current user error:", error);
            // Check if this is a 403 Forbidden error
            if (error.response?.status === 403) {
                console.log("Authentication error: 403 Forbidden. Token may be invalid or expired.");
                // Clear user state and cookies to force re-login
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser();
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                }
            }
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Failed to fetch user details");
        }
    },
    /**
   * Verify current auth session by attempting to fetch user data.
   */ isLoggedIn: async ()=>{
        try {
            await authApi.getCurrentUser(); // Attempt to fetch user data
            return true; // If successful, user is considered logged in
        } catch (error) {
            // Any error (including 401/403 from getCurrentUser) means session is not valid
            return false;
        }
    },
    /**
   * Check if user is authenticated based on cookie presence
   * Uses client-side cookie access when in browser environment
   */ isAuthenticated: async ()=>{
        // Check if we're in a browser environment
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Use server-side cookie access
            const token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAccessToken"])();
            return !!token;
        }
    },
    /**
   * Get the access token from cookies
   * Uses client-side cookie access when in browser environment
   */ getAccessToken: async ()=>{
        // Check if we're in a browser environment
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Use server-side cookie access
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAccessToken"])() || "";
        }
    },
    /**
   * Generate new access token using refresh token
   * @param refreshToken The refresh token to use for generating a new access token
   */ generateAccessToken: async (refreshToken)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axios$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].post(`/auth/access-token`, {}, {
                params: {
                    refresh_token: refreshToken
                }
            });
            // Update the access token in cookies if successful
            if (response.data.success && response.data.access_token) {
                // Calculate token age in seconds from tokenExpireAt
                const expireAt = new Date(response.data.tokenExpireAt).getTime();
                const now = new Date().getTime();
                const accessTokenAge = Math.floor((expireAt - now) / 1000);
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setAuthCookies"])(response.data.access_token, null, accessTokenAge, null);
                // Also update the access token in the user store
                const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().user;
                if (currentUser) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().setUser({
                        ...currentUser,
                        accessToken: response.data.access_token
                    });
                }
            }
            return response.data;
        } catch (error) {
            throw new Error(error.response?.data?.detail || error.response?.data?.message || "Failed to generate new access token");
        }
    },
    /**
   * Initiates the Google OAuth login flow by redirecting the browser.
   */ googleLogin: async ()=>{
        try {
            // Construct the full URL to the backend's Google login initiation endpoint.
            let googleLoginUrl = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_BASE_URL"]}/auth/google-login`;
            // Redirect the user's browser.
            window.location.href = googleLoginUrl;
        } catch (error) {
            console.error("Error during Google login:", error);
            // Fallback to basic Google login without FCM token
            window.location.href = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiConfig$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_BASE_URL"]}/auth/google-login`;
        }
    },
    /**
   * Handles the final steps after Google OAuth callback.
   * Assumes the backend set auth cookies before redirecting back to the frontend.
   */ finalizeGoogleLogin: async ()=>{
        try {
            // Fetch user details using the cookies potentially set by the backend callback
            const userDetails = await authApi.getCurrentUser();
            // Get the access token from cookies
            const accessToken = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAccessToken"])();
            // Update global user store with user details and access token
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().setUser({
                fullName: userDetails.fullName,
                email: userDetails.email,
                accessToken: accessToken
            });
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$userStore$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["useUserStore"].getState().clearUser(); // Reset state on error
            console.error("Failed to retrieve user details after Google login:", error);
        }
    }
};
const __TURBOPACK__default__export__ = authApi;
}}),
"[project]/src/app/api/auth/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Authentication API Route Handler
 *
 * This file provides API routes for authentication operations, acting as a server-side
 * proxy between the client and the external authentication API.
 */ __turbopack_context__.s({
    "GET": (()=>GET),
    "OPTIONS": (()=>OPTIONS),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authApi$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authApi.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/cookies.ts [app-route] (ecmascript)");
;
;
;
;
async function POST(request) {
    try {
        const { action, ...data } = await request.json();
        switch(action){
            case 'login':
                {
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authApi$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].login(data);
                    // Set cookies on the server side
                    if (result.loginData.access_token) {
                        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setAuthCookies"])(result.loginData.access_token, result.loginData.refresh_token, result.loginData.accessTokenAge || 3600, result.loginData.refreshTokenAge || 86400);
                    }
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: true,
                        redirectPath: result.redirectPath || '/workflows'
                    });
                }
            case 'signup':
                {
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authApi$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].signup(data);
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: true,
                        message: result.message || 'Registration successful'
                    });
                }
            case 'logout':
                {
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: true,
                        message: 'Logged out successfully'
                    });
                }
            case 'refresh':
                {
                    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
                    const refreshToken = cookieStore.get('refreshToken')?.value;
                    if (!refreshToken) {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            success: false,
                            message: 'No refresh token found'
                        }, {
                            status: 401
                        });
                    }
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authApi$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].generateAccessToken(refreshToken);
                    if (result.success && result.access_token) {
                        // Set the new access token in cookies
                        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$cookies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setAuthCookies"])(result.access_token, refreshToken, 3600, 86400 // Default refresh token age
                        );
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            success: true,
                            message: 'Token refreshed successfully'
                        });
                    } else {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            success: false,
                            message: 'Failed to refresh token'
                        }, {
                            status: 401
                        });
                    }
                }
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    message: 'Invalid action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Error in auth API route:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error.message || 'Authentication failed',
            details: error.response?.data || null
        }, {
            status: error.response?.status || 500
        });
    }
}
async function GET() {
    try {
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authApi$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authApi"].getCurrentUser();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            user
        });
    } catch (error) {
        console.error('Error fetching current user:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error.message || 'Failed to fetch user',
            details: error.response?.data || null
        }, {
            status: error.response?.status || 500
        });
    }
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__b4965dc2._.js.map