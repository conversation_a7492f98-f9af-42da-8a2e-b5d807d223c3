{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "fc6db8b166867088f2388bf3841404e8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b422f981992d7a2552f6173a326e14246fc05692c86257d29ba982df3ab7190b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3959af6c3a86f5619cbb682051819d1354994907504c45df4896d9a5d58642fb"}}}, "sortedMiddleware": ["/"], "functions": {}}